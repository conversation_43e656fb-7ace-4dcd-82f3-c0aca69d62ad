# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

always response in 中文。

## 项目概述

这是一个基于uni-app框架开发的儿童心理测评移动应用，主要包含以下功能模块：

- **IVA-CPT测评**：儿童注意力持续性测试
- **SNAP-IV量表评估**：父母评定问卷
- **PPVT测评**：图片词汇测试
- **BCI设备集成**：脑机接口设备连接和数据采集
- **报告生成**：测评结果报告查看和导出

## 技术架构

### 开发框架
- **uni-app (Vue 3)**: 跨平台应用开发框架
- **Pinia**: 状态管理
- **uv-ui**: UI组件库
- **原生插件**: 集成深湾脑波设备SDK (alghelper)

### 项目结构
```
├── pages/              # 页面目录
│   ├── login/         # 登录页面
│   ├── home/          # 首页
│   ├── device/        # 设备管理
│   ├── ivacpt/        # IVA-CPT测评相关页面
│   ├── scale/         # SNAP-IV量表评估
│   ├── question/      # PPVT测评
│   ├── record/        # 测评记录
│   └── report/        # 报告查看
├── service/           # API服务层
├── utils/             # 工具函数
├── stores/            # Pinia状态管理
├── components/        # 公共组件
├── static/            # 静态资源
└── uni_modules/       # uni-app插件模块
```

### 核心业务模块

1. **蓝牙设备管理** (`utils/bluConfig.js`, `utils/bluType.js`)
   - 脑波设备连接和通信
   - 数据采集和处理

2. **数据处理算法**
   - `utils/bandpass_filter_offline.js`: 带通滤波器
   - `utils/douglas_peuker.js`: 道格拉斯-普克算法
   - `utils/abnormal.js`: 异常数据检测

3. **WebSocket通信** (`utils/websocket.js`)
   - 实时数据传输
   - 设备状态同步

4. **测评数据管理**
   - `pages/ivacpt/ivaData.js`: IVA-CPT测试数据
   - 音频资源管理: `static/audio/`
   - 图片资源管理: `static/question/`

## 开发环境配置

### 开发工具
使用 **HBuilderX** 进行开发，这是uni-app官方推荐的IDE。

### 运行项目
1. 在HBuilderX中打开项目
2. 选择运行平台：
   - 运行到浏览器 (开发调试)
   - 运行到手机或模拟器 (真机测试)
   - 发行为原生App (打包发布)

### 构建命令
由于这是uni-app项目，构建过程通过HBuilderX的图形界面完成：
- **运行**: 点击工具栏"运行"按钮
- **发行**: 点击"发行" → "原生App-云打包"或"原生App-离线打包"

## 重要配置文件

- `manifest.json`: 应用配置，包含权限、图标、版本等信息
- `pages.json`: 页面路由和导航配置
- `uni.scss`: 全局样式配置

## API服务

### 基础配置
- HTTP请求封装: `utils/interceptors.js`
- 请求工具: `utils/request.js`

### 服务模块
- `service/index.js`: 主要业务API
- `service/ivacpt.js`: IVA-CPT相关API  
- `service/scale.js`: 量表评估API
- `service/eegassess.js`: 脑电评估API

## 注意事项

### 平台特性
- 应用强制横屏显示 (`pageOrientation: "landscape"`)
- 集成蓝牙和相机权限
- 支持全屏模式

### 原生插件
- `nativeplugins/alghelper/`: 深湾脑波设备SDK
- 需要在manifest.json中配置相应权限

### 开发规范
- 使用Vue 3 Composition API
- 组件命名采用PascalCase
- 工具函数放在utils目录下
- API调用统一通过service层

## 测试和调试

### 调试方式
1. 浏览器调试：运行到Chrome等浏览器进行功能调试
2. 真机调试：通过HBuilderX连接真机进行完整功能测试
3. 模拟器调试：使用Android/iOS模拟器

### 关键调试点
- 蓝牙设备连接状态
- 音频播放功能
- 数据采集准确性
- 网络请求响应

## 部署发布

### 打包配置
在manifest.json中配置：
- 应用基本信息
- 权限设置
- 图标和启动图
- 证书配置

### 发布流程
1. 在HBuilderX中选择"发行" → "原生App-云打包"
2. 选择打包类型（测试版或正式版）
3. 配置签名证书
4. 提交打包任务

## 业务流程理解

### IVA-CPT测评流程
1. 设备连接检查
2. 测评介绍和练习
3. 正式测评执行
4. 数据采集和分析
5. 结果报告生成

### 量表评估流程
1. 问卷题目展示
2. 用户选择答案
3. 数据计算分析
4. 评估结果展示

本项目专注于儿童心理健康评估，涉及专业的心理测量工具和脑电数据处理技术。在开发时需要注意数据安全性和测评结果的准确性。

## 文档

目录 /Users/<USER>/repository/naoyu-documentation/ppvt 存放项目开发过程中产生的文档。 如果要求生成文档，则统一存放到这个目录中，你可以通过filesystem的mcp server进行读取和修改。

## 设计修改
如果要求按照figma样式进行修改。 如果UI界面顶部有状态栏、电量、WiFi等标志，其只是参考，开发中不要将这些样式写入到真正的UI中。 当前项目的宽度基准为 1334 prx * 750 rpx 。 