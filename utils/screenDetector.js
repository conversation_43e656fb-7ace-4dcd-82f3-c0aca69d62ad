/*
 * @Description: 屏幕尺寸检测工具
 * @Author: 小雨
 * @Date: 2024-12-28
 * @LastEditTime: 2024-12-28
 * @LastEditors: 小雨
 */

/**
 * 获取屏幕信息并计算物理尺寸
 * @returns {Promise<Object>} 屏幕信息对象
 */
export function getScreenInfo() {
    return new Promise((resolve) => {
        // 获取系统信息
        uni.getSystemInfo({
            success: (systemInfo) => {
                const {
                    screenWidth,
                    screenHeight,
                    pixelRatio,
                    windowWidth,
                    windowHeight,
                    platform,
                    system,
                    brand,
                    model
                } = systemInfo;

                // 计算实际分辨率（四舍五入到整数）
                const actualWidth = Math.round(screenWidth * pixelRatio);
                const actualHeight = Math.round(screenHeight * pixelRatio);

                // 计算屏幕对角线像素数
                const diagonalPixels = Math.sqrt(actualWidth * actualWidth + actualHeight * actualHeight);

                // 精确DPI计算
                let estimatedDPI = 96; // 默认DPI
                
                // 常见设备DPI数据库（基于实际设备规格）
                const deviceDPIDatabase = [
                    // 11寸平板常见规格
                    { width: 1920, height: 1200, diagonal: 11, dpi: 218 },
                    { width: 2000, height: 1200, diagonal: 11, dpi: 227 },
                    { width: 2560, height: 1600, diagonal: 11, dpi: 290 },
                    
                    // 10寸平板
                    { width: 1920, height: 1200, diagonal: 10.1, dpi: 237 },
                    { width: 1280, height: 800, diagonal: 10.1, dpi: 149 },
                    
                    // 12寸平板/笔记本
                    { width: 2160, height: 1440, diagonal: 12, dpi: 216 },
                    { width: 2736, height: 1824, diagonal: 12.3, dpi: 267 },
                    
                    // 13寸笔记本
                    { width: 1920, height: 1080, diagonal: 13.3, dpi: 166 },
                    { width: 2560, height: 1600, diagonal: 13.3, dpi: 227 },
                    
                    // 15.6寸笔记本
                    { width: 1920, height: 1080, diagonal: 15.6, dpi: 141 },
                    { width: 2560, height: 1440, diagonal: 15.6, dpi: 188 },
                    
                    // 手机常见规格
                    { width: 1080, height: 2340, diagonal: 6.67, dpi: 386 },
                    { width: 1080, height: 2400, diagonal: 6.81, dpi: 386 },
                ];
                
                // 尝试精确匹配设备规格
                let matchedDevice = null;
                const tolerance = 50; // 像素容差
                
                for (const device of deviceDPIDatabase) {
                    const widthMatch = Math.abs(actualWidth - device.width) <= tolerance || 
                                     Math.abs(actualWidth - device.height) <= tolerance;
                    const heightMatch = Math.abs(actualHeight - device.height) <= tolerance || 
                                      Math.abs(actualHeight - device.width) <= tolerance;
                    
                    if (widthMatch && heightMatch) {
                        matchedDevice = device;
                        estimatedDPI = device.dpi;
                        break;
                    }
                }
                
                // 如果没有精确匹配，使用改进的估算算法
                if (!matchedDevice) {
                    if (platform === 'ios') {
                        // iOS设备DPI估算
                        if (pixelRatio >= 3) {
                            estimatedDPI = 458; // iPhone Plus/Pro Max类型
                        } else if (pixelRatio >= 2) {
                            estimatedDPI = 326; // 标准Retina屏幕
                        } else {
                            estimatedDPI = 163; // 非Retina屏幕
                        }
                    } else if (platform === 'android') {
                        // Android设备改进的DPI估算
                        // 根据分辨率和像素密度比综合判断
                        if (actualWidth >= 1800 && actualHeight >= 1000) {
                            // 高分辨率平板
                            if (pixelRatio >= 2.5) {
                                estimatedDPI = 280; // 高密度平板
                            } else if (pixelRatio >= 2) {
                                estimatedDPI = 220; // 中高密度平板
                            } else {
                                estimatedDPI = 180; // 标准平板
                            }
                        } else if (actualWidth >= 1000) {
                            // 手机或小平板
                            if (pixelRatio >= 3.5) {
                                estimatedDPI = 480; // XXHDPI
                            } else if (pixelRatio >= 3) {
                                estimatedDPI = 420; // 高密度
                            } else if (pixelRatio >= 2) {
                                estimatedDPI = 320; // XHDPI
                            } else if (pixelRatio >= 1.5) {
                                estimatedDPI = 240; // HDPI
                            } else {
                                estimatedDPI = 160; // MDPI
                            }
                        } else {
                            // 低分辨率设备
                            estimatedDPI = 160;
                        }
                    } else {
                        // 桌面端或其他平台
                        if (actualWidth >= 2560 && actualHeight >= 1440) {
                            estimatedDPI = 109; // 27寸 2K显示器
                        } else if (actualWidth >= 1920 && actualHeight >= 1080) {
                            if (actualWidth >= 3840) {
                                estimatedDPI = 163; // 27寸 4K显示器
                            } else {
                                estimatedDPI = 96; // 标准1080p显示器
                            }
                        } else {
                            estimatedDPI = 96; // 默认
                        }
                    }
                }

                // 计算物理尺寸（英寸）
                const physicalDiagonalInches = diagonalPixels / estimatedDPI;
                
                // 根据对角线尺寸判断设备类型和常见尺寸
                let deviceType = '';
                let commonSize = '';
                
                if (physicalDiagonalInches < 7) {
                    deviceType = '手机';
                    if (physicalDiagonalInches < 5) {
                        commonSize = '小屏手机 (~4.7寸)';
                    } else if (physicalDiagonalInches < 6) {
                        commonSize = '标准手机 (~5.5寸)';
                    } else {
                        commonSize = '大屏手机 (~6.5寸)';
                    }
                } else if (physicalDiagonalInches < 13) {
                    deviceType = '平板电脑';
                    if (physicalDiagonalInches < 9) {
                        commonSize = '小平板 (~7-8寸)';
                    } else if (physicalDiagonalInches < 11) {
                        commonSize = '中等平板 (~9-10寸)';
                    } else {
                        commonSize = '大平板 (~11-12寸)';
                    }
                } else {
                    deviceType = '电脑显示器/笔记本';
                    if (physicalDiagonalInches < 14) {
                        commonSize = '小笔记本 (~13寸)';
                    } else if (physicalDiagonalInches < 16) {
                        commonSize = '标准笔记本 (~15.6寸)';
                    } else if (physicalDiagonalInches < 18) {
                        commonSize = '大笔记本 (~17寸)';
                    } else if (physicalDiagonalInches < 22) {
                        commonSize = '小显示器 (~21寸)';
                    } else if (physicalDiagonalInches < 25) {
                        commonSize = '标准显示器 (~24寸)';
                    } else if (physicalDiagonalInches < 28) {
                        commonSize = '大显示器 (~27寸)';
                    } else {
                        commonSize = '超大显示器 (~32寸+)';
                    }
                }

                // 计算屏幕比例
                const aspectRatio = (actualWidth / actualHeight).toFixed(2);
                let ratioDescription = '';
                if (Math.abs(aspectRatio - 1.78) < 0.1) {
                    ratioDescription = '16:9';
                } else if (Math.abs(aspectRatio - 1.6) < 0.1) {
                    ratioDescription = '16:10';
                } else if (Math.abs(aspectRatio - 1.33) < 0.1) {
                    ratioDescription = '4:3';
                } else if (Math.abs(aspectRatio - 2.37) < 0.1) {
                    ratioDescription = '21:9 (超宽屏)';
                } else {
                    ratioDescription = `${aspectRatio}:1`;
                }

                const screenInfo = {
                    // 基础信息
                    platform,
                    system,
                    brand,
                    model,
                    
                    // 逻辑分辨率
                    logicalWidth: screenWidth,
                    logicalHeight: screenHeight,
                    
                    // 物理分辨率
                    physicalWidth: actualWidth,
                    physicalHeight: actualHeight,
                    
                    // 像素密度
                    pixelRatio,
                    estimatedDPI,
                    
                    // 物理尺寸
                    diagonalInches: physicalDiagonalInches,
                    deviceType,
                    commonSize,
                    
                    // 屏幕比例
                    aspectRatio: parseFloat(aspectRatio),
                    ratioDescription,
                    
                    // 窗口信息
                    windowWidth,
                    windowHeight,
                    
                    // 匹配信息
                    matchedDevice,
                    isExactMatch: !!matchedDevice
                };

                resolve(screenInfo);
            },
            fail: (error) => {
                console.error('获取系统信息失败:', error);
                resolve(null);
            }
        });
    });
}

/**
 * 打印屏幕分辨率信息到控制台
 * @param {Object} screenInfo 屏幕信息对象
 */
export function printScreenInfo(screenInfo) {
    if (!screenInfo) {
        console.error('❌ 无法获取屏幕信息');
        return;
    }

    console.log('\n🖥️ ==================== 屏幕分辨率信息 ====================');
    console.log(`📐 逻辑分辨率: ${screenInfo.logicalWidth} × ${screenInfo.logicalHeight}`);
    console.log(`📐 物理分辨率: ${screenInfo.physicalWidth} × ${screenInfo.physicalHeight}`);
    console.log(`📐 像素密度比: ${screenInfo.pixelRatio}`);
    console.log('========================================================\n');
}

/**
 * 初始化屏幕检测
 * 在应用启动时调用，自动检测并打印分辨率信息
 */
export async function initScreenDetection() {
    try {
        console.log('🔍 开始检测屏幕分辨率...');
        const screenInfo = await getScreenInfo();
        
        if (screenInfo) {
            printScreenInfo(screenInfo);
            return screenInfo;
        } else {
            console.error('❌ 屏幕分辨率检测失败');
            return null;
        }
    } catch (error) {
        console.error('❌ 屏幕检测过程中发生错误:', error);
        return null;
    }
}
