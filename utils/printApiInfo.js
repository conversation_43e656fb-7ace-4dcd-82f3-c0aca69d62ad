/*
 * @Description: 打印API请求信息工具
 * @Author: 系统生成
 * @Date: 2025-08-06
 * @LastEditTime: 2025-08-06
 * @LastEditors: 系统生成
 */

import {
    getApiLogs,
    exportApiLogs,
    getApiLogConfig,
    setApiLogConfig,
    enableApiLog,
    disableApiLog
} from './apiLogger';

/**
 * 打印所有API请求信息
 * 每个请求之间用分割线隔开，以完整合法的JSON格式显示
 */
const printAllApiInfo = () => {
    const logs = getApiLogs();
    
    if (logs.length === 0) {
        console.log('暂无API请求记录');
        return;
    }
    
    console.log('=== 所有API请求信息 ===');
    
    logs.forEach((log, index) => {
        // 打印分割线
        console.log('========================================');
        console.log(`API请求 #${index + 1}`);
        console.log('========================================');
        
        // 打印请求信息
        console.log('请求信息:');
        console.log(JSON.stringify({
            url: log.request.url,
            method: log.request.method,
            headers: log.request.headers,
            body: log.request.body
        }, null, 2));
        
        // 打印响应信息
        console.log('\n响应信息:');
        console.log(JSON.stringify(log.response, null, 2));
        
        // 打印分割线
        console.log('========================================\n');
    });
    
    console.log(`=== 共 ${logs.length} 个API请求 ===`);
};

/**
 * 导出所有API日志为JSON字符串
 * @returns {String} JSON格式的日志字符串
 */
const exportAllApiLogs = () => {
    return exportApiLogs();
};

/**
 * 清空所有API日志
 */
const clearAllApiLogs = () => {
    const logs = getApiLogs();
    const count = logs.length;
    
    // 清空日志
    logs.length = 0;
    
    console.log(`已清空 ${count} 条API日志记录`);
};

/**
 * 获取API日志统计信息
 * @returns {Object} 统计信息
 */
const getApiStats = () => {
    const logs = getApiLogs();
    const stats = {
        total: logs.length,
        methods: {},
        urls: {},
        statusCodes: {}
    };
    
    logs.forEach(log => {
        // 统计请求方法
        const method = log.request.method;
        stats.methods[method] = (stats.methods[method] || 0) + 1;
        
        // 统计URL
        const url = log.request.url;
        stats.urls[url] = (stats.urls[url] || 0) + 1;
        
        // 统计状态码
        if (log.response && log.response.statusCode) {
            const statusCode = log.response.statusCode;
            stats.statusCodes[statusCode] = (stats.statusCodes[statusCode] || 0) + 1;
        }
    });
    
    return stats;
};

/**
 * 打印API统计信息
 */
const printApiStats = () => {
    const stats = getApiStats();
    
    console.log('=== API请求统计信息 ===');
    console.log(`总请求数: ${stats.total}`);
    
    console.log('\n请求方法统计:');
    Object.entries(stats.methods).forEach(([method, count]) => {
        console.log(`  ${method}: ${count}`);
    });
    
    console.log('\nURL统计:');
    Object.entries(stats.urls).forEach(([url, count]) => {
        console.log(`  ${url}: ${count}`);
    });
    
    console.log('\n状态码统计:');
    Object.entries(stats.statusCodes).forEach(([statusCode, count]) => {
        console.log(`  ${statusCode}: ${count}`);
    });
    
    console.log('=========================');
};

// 全局导出函数，方便在控制台调用
if (typeof window !== 'undefined') {
    window.printAllApiInfo = printAllApiInfo;
    window.exportAllApiLogs = exportAllApiLogs;
    window.clearAllApiLogs = clearAllApiLogs;
    window.printApiStats = printApiStats;
}

/**
 * 获取API日志配置
 * @returns {Object} 配置对象
 */
const getApiConfig = () => {
    return getApiLogConfig();
};

/**
 * 设置API日志配置
 * @param {Object} config - 配置对象
 */
const setApiConfig = (config) => {
    setApiLogConfig(config);
    console.log('API日志配置已更新:', getApiLogConfig());
};

/**
 * 启用API日志记录
 */
const enableApiLogging = () => {
    enableApiLog();
    console.log('API日志记录已启用');
};

/**
 * 禁用API日志记录
 */
const disableApiLogging = () => {
    disableApiLog();
    console.log('API日志记录已禁用');
};

/**
 * 切换API日志记录状态
 */
const toggleApiLogging = () => {
    const config = getApiLogConfig();
    if (config.enabled) {
        disableApiLogging();
    } else {
        enableApiLogging();
    }
};

/**
 * 启用控制台打印
 */
const enableConsolePrint = () => {
    setApiLogConfig({ printToConsole: true });
    console.log('控制台打印已启用');
};

/**
 * 禁用控制台打印
 */
const disableConsolePrint = () => {
    setApiLogConfig({ printToConsole: false });
    console.log('控制台打印已禁用');
};

/**
 * 启用本地存储
 */
const enableStorageSave = () => {
    setApiLogConfig({ saveToStorage: true });
    console.log('本地存储已启用');
};

/**
 * 禁用本地存储
 */
const disableStorageSave = () => {
    setApiLogConfig({ saveToStorage: false });
    console.log('本地存储已禁用');
};

// 全局导出函数，方便在控制台调用
if (typeof window !== 'undefined') {
    window.printAllApiInfo = printAllApiInfo;
    window.exportAllApiLogs = exportAllApiLogs;
    window.clearAllApiLogs = clearAllApiLogs;
    window.printApiStats = printApiStats;
    window.getApiConfig = getApiConfig;
    window.setApiConfig = setApiConfig;
    window.enableApiLogging = enableApiLogging;
    window.disableApiLogging = disableApiLogging;
    window.toggleApiLogging = toggleApiLogging;
    window.enableConsolePrint = enableConsolePrint;
    window.disableConsolePrint = disableConsolePrint;
    window.enableStorageSave = enableStorageSave;
    window.disableStorageSave = disableStorageSave;
}

export {
    printAllApiInfo,
    exportAllApiLogs,
    clearAllApiLogs,
    getApiStats,
    printApiStats,
    getApiConfig,
    setApiConfig,
    enableApiLogging,
    disableApiLogging,
    toggleApiLogging,
    enableConsolePrint,
    disableConsolePrint,
    enableStorageSave,
    disableStorageSave
};