/*
 * @Description:封装接口
 * @Author: 小雨
 * @Date: 2023-03-08 09:12:35
 * @LastEditTime: 2023-03-08 13:23:47
 * @LastEditors: 小雨
 */

import {
	showLoading
} from '@/common/uniTool.js';
import {
	baseUrl
} from '@/common/global.js'
const config = Symbol('config');
const isCompleteURL = Symbol('isCompleteURL');
const requestBefore = Symbol('requestBefore');
const requestAfter = Symbol('requestAfter');

class httpRequest {
	//默认配置
	[config] = {
		baseURL: baseUrl,
		header: {
			'content-type': 'application/json',
			'sourceType': 'PAD',
		},
		data: {},
		method: 'GET',
		dataType: 'json',
		responseType: 'text',
	};
	//拦截器
	interceptors = {
		request: (func) => {
			if (func) {
				httpRequest[requestBefore] = func;
			} else {
				httpRequest[requestBefore] = (request) => request;
			}
		},
		response: (func) => {
			if (func) {
				httpRequest[requestAfter] = func;
			} else {
				httpRequest[requestAfter] = (response) => response;
			}
		},
	};
	static[requestBefore](config) {
		return config;
	}
	static[requestAfter](response) {
		return response;
	}
	static[isCompleteURL](url) {
		return /http[s]{0,1}:\/\/([\w.]+\/?)\S*/.test(url);
	}

	request(options = {}) {
		options.baseURL = options.baseURL || this[config].baseURL;
		options.dataType = options.dataType || this[config].dataType;
		options.url = httpRequest[isCompleteURL](options.url) ? options.url : options.baseURL + options.url;
		options.data = options.data;
		options.header = {
			...this[config].header,
			...options.header,
		};
		options.method = options.method || this[config].method;
		options = {
			...options,
			...httpRequest[requestBefore](options)
		};
		return new Promise((resolve, reject) => {
			if (!options.hideLoading) {
				showLoading('加载中...');
			}
			options.success = function (res) {
				// 将请求配置添加到响应对象中，以便在响应拦截器中使用
				res.config = {
					_requestInfo: {
						url: options.url,
						method: options.method,
						headers: options.header,
						data: options.data
					}
				};
				resolve(httpRequest[requestAfter](res));
			};
			options.fail = function (err) {
				// 将请求配置添加到错误对象中
				err.config = {
					_requestInfo: {
						url: options.url,
						method: options.method,
						headers: options.header,
						data: options.data
					}
				};
				reject(httpRequest[requestAfter](err));
			};
			uni.request(options);
		});
	}
	get(url, data, options = {}) {
		options.url = url;
		options.data = data;
		options.method = 'GET';
		return this.request(options);
	}
	post(url, data, options = {}) {
		options.url = url;
		options.data = data;
		options.method = 'POST';
		return this.request(options);
	}
}

export default new httpRequest();