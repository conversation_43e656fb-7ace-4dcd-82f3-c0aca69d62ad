/*
 * @Description:拦截器
 * @Author: 小雨
 * @Date: 2023-03-08 10:28:02
 * @LastEditTime: 2023-03-08 13:25:27
 * @LastEditors: 小雨
 */

import httpRequest from '@/utils/request.js';
import {
	hideLoading,
	navigateTo,
	showToast
} from '../common/uniTool';
import {
	pageToLogin
} from './abnormal';
import {
	logApiRequest
} from './apiLogger';

// 请求拦截器
httpRequest.interceptors.request((request) => {
	// 获取登录后存储在本地的token信息
	let token = uni.getStorageSync('token');
	let requestType = request.url.split('/');
	let typeVal = requestType[requestType.length - 1];
	if (typeVal === 'login') {
		// 判断筛选出以上三个页面不需要为请求头设置token，根据自己的项目情况而定
		delete request.header.token
	} else {
		request.header.token = token;
	}
	
	// 存储请求信息以便在响应拦截器中使用
	request._requestInfo = {
		url: request.url,
		method: request.method,
		headers: request.header,
		data: request.data
	};
	
	return request;
});

export const HTTP_STATUS = {
	SUCCESS: 200,
	CREATED: 201,
	ACCEPTED: 202,
	CLIENT_ERROR: 400,
	AUTHENTICATE: 401,
	FORBIDDEN: 403,
	NOT_FOUND: 404,
	SERVER_ERROR: 500,
	BAD_GATEWAY: 502,
	SERVICE_UNAVAILABLE: 503,
	GATEWAY_TIMEOUT: 504,
};
// 响应拦截器
httpRequest.interceptors.response((response) => {
	hideLoading()
	
	// 获取请求信息
	const requestInfo = response.config._requestInfo;
	
	// 记录API请求信息
	if (requestInfo) {
		logApiRequest(
			requestInfo.url,
			requestInfo.method,
			requestInfo.headers,
			requestInfo.data,
			response.data
		);
	}
	
	// 超时重新登录
	if (response.statusCode === HTTP_STATUS.SERVER_ERROR || response.statusCode === HTTP_STATUS.BAD_GATEWAY) {
		return Promise.reject({
			desc: '服务器错误'
		});
	} else if (response.statusCode === HTTP_STATUS.SUCCESS) {
		if (response.data.code === '0000') {
			return response.data;
		} else if (response.data.code == '9998') {
			//非法登录
			showToast('登录过期，请重新点击');
			pageToLogin();
			return Promise.reject(response.data);
		} else {
			return Promise.reject(response.data);
		}
	}
});

export default httpRequest;