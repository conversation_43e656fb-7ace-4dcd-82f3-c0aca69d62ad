/**
 * 响应式适配工具
 * <AUTHOR>
 * @description 基于屏幕尺寸和像素比的动态适配工具
 */

// 设计稿基准尺寸（920x1080，15.6英寸屏幕）
const DESIGN_WIDTH = 920;
const DESIGN_HEIGHT = 1080;
const DESIGN_DPI = 96; // 标准DPI

/**
 * 获取当前设备信息
 */
export function getDeviceInfo() {
    return new Promise((resolve) => {
        // #ifdef H5
        const info = {
            windowWidth: window.innerWidth,
            windowHeight: window.innerHeight,
            pixelRatio: window.devicePixelRatio || 1,
            screenWidth: window.screen.width,
            screenHeight: window.screen.height
        };
        resolve(info);
        // #endif
        
        // #ifdef APP-PLUS
        uni.getSystemInfo({
            success: (res) => {
                resolve({
                    windowWidth: res.windowWidth,
                    windowHeight: res.windowHeight,
                    pixelRatio: res.pixelRatio || 1,
                    screenWidth: res.screenWidth,
                    screenHeight: res.screenHeight
                });
            }
        });
        // #endif
    });
}

/**
 * 检测是否为11英寸高分辨率屏幕
 * @param {Object} deviceInfo 设备信息
 * @returns {boolean} 是否为11英寸屏幕
 */
export function is11InchScreen(deviceInfo) {
    const { windowWidth, windowHeight, pixelRatio } = deviceInfo;
    
    // 11英寸屏幕的典型特征
    const isHighResolution = windowWidth >= 1800 && windowWidth <= 2000 && 
                            windowHeight >= 1100 && windowHeight <= 1300;
    const isHighDPI = pixelRatio >= 1.5;
    
    // 计算物理尺寸（英寸）
    const physicalWidth = windowWidth / (96 * pixelRatio);
    const physicalHeight = windowHeight / (96 * pixelRatio);
    const diagonalInches = Math.sqrt(physicalWidth * physicalWidth + physicalHeight * physicalHeight);
    
    // 11英寸屏幕的对角线尺寸范围
    const is11InchSize = diagonalInches >= 10.5 && diagonalInches <= 12;
    
    return isHighResolution && isHighDPI && is11InchSize;
}

/**
 * 计算缩放比例
 * @param {Object} deviceInfo 设备信息
 * @returns {Object} 缩放比例信息
 */
export function calculateScale(deviceInfo) {
    const { windowWidth, windowHeight, pixelRatio } = deviceInfo;
    
    // 计算物理尺寸（英寸）
    const physicalWidth = windowWidth / (96 * pixelRatio); // 96 DPI 标准
    const physicalHeight = windowHeight / (96 * pixelRatio);
    const diagonalInches = Math.sqrt(physicalWidth * physicalWidth + physicalHeight * physicalHeight);
    
    // 检测11英寸屏幕
    const is11Inch = is11InchScreen(deviceInfo);
    
    // 基于屏幕尺寸的缩放因子
    let sizeScale = 1;
    if (is11Inch) {
        // 11英寸屏幕专用缩放
        sizeScale = 0.75; // 更激进的缩放以优化显示
    } else if (diagonalInches < 13) {
        // 其他小屏幕设备，缩小显示
        sizeScale = 0.75 + (diagonalInches - 10) * 0.083; // 10英寸=0.75, 13英寸=1.0
    } else if (diagonalInches > 17) {
        // 大屏幕设备，放大显示
        sizeScale = 1 + (diagonalInches - 17) * 0.05; // 17英寸=1.0, 每增加1英寸+0.05
    }
    
    // 基于分辨率的缩放因子
    const widthScale = windowWidth / DESIGN_WIDTH;
    const heightScale = windowHeight / DESIGN_HEIGHT;
    const resolutionScale = Math.min(widthScale, heightScale);
    
    // 基于像素比的调整
    const dpiScale = Math.min(pixelRatio / 1.5, 1.2); // 限制最大1.2倍
    
    // 综合缩放比例
    let finalScale;
    if (is11Inch) {
        // 11英寸屏幕使用固定的优化缩放比例
        finalScale = 0.75;
    } else {
        finalScale = sizeScale * resolutionScale * (1 / dpiScale);
        finalScale = Math.max(0.6, Math.min(1.4, finalScale)); // 限制在0.6-1.4倍之间
    }
    
    return {
        sizeScale,
        resolutionScale,
        dpiScale,
        finalScale,
        is11InchScreen: is11Inch,
        deviceInfo: {
            ...deviceInfo,
            physicalWidth,
            physicalHeight,
            diagonalInches
        }
    };
}

/**
 * 转换px值为适配后的值
 * @param {number} px 原始px值
 * @param {number} scale 缩放比例
 * @returns {string} 适配后的CSS值
 */
export function adaptPx(px, scale = 1) {
    return Math.round(px * scale) + 'px';
}

/**
 * 转换字体大小
 * @param {number} fontSize 原始字体大小
 * @param {number} scale 缩放比例
 * @returns {string} 适配后的字体大小
 */
export function adaptFontSize(fontSize, scale = 1) {
    const adaptedSize = Math.round(fontSize * scale);
    return Math.max(12, Math.min(48, adaptedSize)) + 'px'; // 限制字体大小范围
}

/**
 * 批量转换样式对象
 * @param {Object} styles 样式对象
 * @param {number} scale 缩放比例
 * @returns {Object} 适配后的样式对象
 */
export function adaptStyles(styles, scale = 1) {
    const adaptedStyles = {};
    
    for (const [key, value] of Object.entries(styles)) {
        if (typeof value === 'string' && value.endsWith('px')) {
            const pxValue = parseFloat(value);
            if (key.includes('font') || key.includes('Font')) {
                adaptedStyles[key] = adaptFontSize(pxValue, scale);
            } else {
                adaptedStyles[key] = adaptPx(pxValue, scale);
            }
        } else {
            adaptedStyles[key] = value;
        }
    }
    
    return adaptedStyles;
}

/**
 * 创建响应式CSS变量
 * @param {number} scale 缩放比例
 * @returns {string} CSS变量字符串
 */
export function createResponsiveCSSVars(scale = 1) {
    return `
        :root {
            --responsive-scale: ${scale};
            --font-size-xs: ${adaptFontSize(12, scale)};
            --font-size-sm: ${adaptFontSize(14, scale)};
            --font-size-base: ${adaptFontSize(16, scale)};
            --font-size-lg: ${adaptFontSize(18, scale)};
            --font-size-xl: ${adaptFontSize(20, scale)};
            --font-size-xxl: ${adaptFontSize(22, scale)};
            --font-size-title: ${adaptFontSize(30, scale)};
            
            --spacing-xs: ${adaptPx(4, scale)};
            --spacing-sm: ${adaptPx(8, scale)};
            --spacing-base: ${adaptPx(12, scale)};
            --spacing-lg: ${adaptPx(16, scale)};
            --spacing-xl: ${adaptPx(24, scale)};
            --spacing-xxl: ${adaptPx(32, scale)};
            
            --border-radius-sm: ${adaptPx(4, scale)};
            --border-radius-base: ${adaptPx(8, scale)};
            --border-radius-lg: ${adaptPx(12, scale)};
            
            --header-height: ${adaptPx(82, scale)};
            --status-bar-height: ${adaptPx(44, scale)};
            --table-cell-height: ${adaptPx(61, scale)};
            --button-height: ${adaptPx(44, scale)};
        }
    `;
}

// 全局缓存缩放信息
let globalScale = null;

/**
 * 初始化响应式适配
 */
export async function initResponsive() {
    try {
        const deviceInfo = await getDeviceInfo();
        const scaleInfo = calculateScale(deviceInfo);
        globalScale = scaleInfo.finalScale;
        
        // 注入CSS变量
        const cssVars = createResponsiveCSSVars(globalScale);
        
        // #ifdef H5
        if (typeof document !== 'undefined') {
            let styleEl = document.getElementById('responsive-vars');
            if (!styleEl) {
                styleEl = document.createElement('style');
                styleEl.id = 'responsive-vars';
                document.head.appendChild(styleEl);
            }
            styleEl.innerHTML = cssVars;
            
            // 为11英寸屏幕添加特殊CSS类
            if (scaleInfo.is11InchScreen) {
                document.documentElement.classList.add('screen-11inch');
                console.log('检测到11英寸屏幕，已应用专用优化样式');
            }
        }
        // #endif
        
        console.log('响应式适配初始化完成:', {
            deviceInfo,
            scaleInfo,
            finalScale: globalScale,
            is11InchScreen: scaleInfo.is11InchScreen
        });
        
        return scaleInfo;
    } catch (error) {
        console.error('响应式适配初始化失败:', error);
        globalScale = 1;
        return { finalScale: 1, is11InchScreen: false };
    }
}

/**
 * 获取当前缩放比例
 */
export function getCurrentScale() {
    return globalScale || 1;
}

/**
 * 响应式混入对象（Vue组件使用）
 */
export const responsiveMixin = {
    data() {
        return {
            responsiveScale: 1
        };
    },
    async created() {
        const scaleInfo = await initResponsive();
        this.responsiveScale = scaleInfo.finalScale;
    },
    methods: {
        adaptPx(px) {
            return adaptPx(px, this.responsiveScale);
        },
        adaptFontSize(fontSize) {
            return adaptFontSize(fontSize, this.responsiveScale);
        },
        adaptStyles(styles) {
            return adaptStyles(styles, this.responsiveScale);
        }
    }
};
