/*
 * @Description: API请求日志记录器
 * @Author: 系统生成
 * @Date: 2025-08-06
 * @LastEditTime: 2025-08-06
 * @LastEditors: 系统生成
 */

import {
    baseUrl
} from '@/common/global.js';

// 存储所有API请求信息
const apiLogs = [];

// API日志记录开关配置
let apiLogConfig = {
    enabled: true,           // 是否启用API日志记录
    printToConsole: true,   // 是否打印到控制台
    saveToStorage: false,   // 是否保存到本地存储
    maxLogs: 100            // 最大日志数量
};

/**
 * 获取API日志配置
 * @returns {Object} 配置对象
 */
const getApiLogConfig = () => {
    return { ...apiLogConfig };
};

/**
 * 设置API日志配置
 * @param {Object} config - 新的配置对象
 */
const setApiLogConfig = (config) => {
    apiLogConfig = { ...apiLogConfig, ...config };
    
    // 如果配置改变，保存到本地存储
    if (apiLogConfig.saveToStorage) {
        try {
            uni.setStorageSync('apiLogConfig', apiLogConfig);
        } catch (e) {
            console.error('保存API日志配置失败:', e);
        }
    }
};

/**
 * 启用API日志记录
 */
const enableApiLog = () => {
    setApiLogConfig({ enabled: true });
};

/**
 * 禁用API日志记录
 */
const disableApiLog = () => {
    setApiLogConfig({ enabled: false });
};

/**
 * 初始化API日志配置
 */
const initApiLogConfig = () => {
    try {
        // 尝试从本地存储读取配置
        const savedConfig = uni.getStorageSync('apiLogConfig');
        if (savedConfig) {
            apiLogConfig = { ...apiLogConfig, ...savedConfig };
        }
    } catch (e) {
        console.error('读取API日志配置失败:', e);
    }
};

/**
 * 格式化JSON字符串
 * @param {Object} obj - 要格式化的对象
 * @returns {String} 格式化后的JSON字符串
 */
const formatJSON = (obj) => {
    try {
        return JSON.stringify(obj, null, 2);
    } catch (e) {
        return String(obj);
    }
};

/**
 * 打印分割线
 */
const printDivider = () => {
    console.log('========================================');
};

/**
 * 记录API请求信息
 * @param {String} url - 请求URL
 * @param {String} method - 请求方法
 * @param {Object} headers - 请求头
 * @param {Object} data - 请求数据
 * @param {Object} response - 响应数据
 */
const logApiRequest = (url, method, headers, data, response) => {
    // 检查是否启用API日志记录
    if (!apiLogConfig.enabled) {
        return;
    }
    
    const fullUrl = url.startsWith('http') ? url : baseUrl + url;
    const logEntry = {
        timestamp: new Date().toISOString(),
        request: {
            url: fullUrl,
            method: method,
            headers: headers,
            body: data
        },
        response: response
    };
    
    // 添加到日志数组
    apiLogs.push(logEntry);
    
    // 限制日志数量
    if (apiLogs.length > apiLogConfig.maxLogs) {
        apiLogs.splice(0, apiLogs.length - apiLogConfig.maxLogs);
    }
    
    // 打印到控制台
    if (apiLogConfig.printToConsole) {
        printDivider();
        console.log('API请求信息:');
        console.log('时间戳:', logEntry.timestamp);
        console.log('完整地址:', fullUrl);
        console.log('请求方法:', method);
        console.log('请求头:');
        console.log(formatJSON(headers));
        console.log('请求参数:');
        console.log(formatJSON(data));
        console.log('响应数据:');
        console.log(formatJSON(response));
        printDivider();
    }
    
    // 保存到本地存储
    if (apiLogConfig.saveToStorage) {
        try {
            uni.setStorageSync('apiLogs', apiLogs);
        } catch (e) {
            console.error('保存API日志失败:', e);
        }
    }
};

/**
 * 获取所有API日志
 * @returns {Array} API日志数组
 */
const getApiLogs = () => {
    return apiLogs;
};

/**
 * 清空API日志
 */
const clearApiLogs = () => {
    apiLogs.length = 0;
};

/**
 * 导出所有API日志为JSON字符串
 * @returns {String} JSON格式的日志字符串
 */
const exportApiLogs = () => {
    return formatJSON(apiLogs);
};

// 初始化配置
initApiLogConfig();

export {
    logApiRequest,
    getApiLogs,
    clearApiLogs,
    exportApiLogs,
    getApiLogConfig,
    setApiLogConfig,
    enableApiLog,
    disableApiLog,
    initApiLogConfig
};