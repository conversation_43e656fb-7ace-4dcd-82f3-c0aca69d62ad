<template>
	<view class="steps">
		<view v-for="(item,index) in props.options" class="steps-item" :key="index">
			<view class="steps-item-num " :class="props.active>=index+1?'steps-item-num-active':''">
				<view class="steps-item-num-text center" :class="props.active>=index+1?'steps-item-num-text-active':''">{{index+1}}</view>
				<view class="steps-item-num-wave" :style="{width:props.type?'680rpx':'',right:props.type?'-688rpx':''}" :class="props.active>index+1?'steps-item-num-active':''"
					v-if="index<props.options.length-1">

				</view>
				<view class="steps-item-title" :class="props.active>=index+1?'steps-item-title-active':''">
					{{item.title}}
				</view>
			</view>


		</view>
	</view>
</template>

<script setup>
	import {
		reactive
	} from "vue";
	const props = defineProps(['options', 'active', 'type'])
</script>

<style lang="scss">
	.steps {
		display: flex;
		align-items: center;
		justify-content: space-between;
		width: 90vw;
		margin: 0 auto;
		margin-top: 10rpx;

		&-item {
			display: flex;
			flex-direction: column;
			align-items: center;

			&-title {
				font-family: SourceHanSansCN, SourceHanSansCN;
				font-weight: bold;
				font-size: 38rpx;
				color: #999999;
				margin-left: 12rpx;

				&-active {
					color: #287FFF;
				}
			}

			&-num {
				background: #FFFFFF;
				border-radius: 30rpx;
				height: 66rpx;
				position: relative;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 16rpx 0 6rpx;

				&-text {
					width: 52rpx;
					height: 52rpx;
					background: #999999;
					font-family: SourceHanSansCN, SourceHanSansCN;
					font-weight: bold;
					font-size: 45rpx;
					color: #FFFFFF;
					border-radius: 50%;

					&-active {
						background: #287FFF;
					}
				}

				&-wave {
					width: 490rpx;
					height: 6rpx;
					background: #D8D8D8;
					position: absolute;
					right: -498rpx;
					top: 50%;
					transform: translateY(-50%);
				}

				&-active {
					background: #FFFFFF;
					color: #287FFF;
				}

			}
		}
	}
</style>