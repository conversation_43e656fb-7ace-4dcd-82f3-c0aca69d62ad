<template>
	<view class="pc_clickEffect" id="pc_clickEffect" :isClick="isClick" :change:isClick="pc_clickEffect.changeIsClick"></view>
</template>

<script>
	export default {
		name: 'pc-clickEffect',
		props: {
			isClick: Boolean
		}
	}
</script>

<script module="pc_clickEffect" lang="renderjs">
	export default {
		props: {
			color: {
				type: String,
				default: '#409EFF'
			},
			model: {
				type: String,
				default: 'default'
			},
			icon: {
				type: String,
				default: ''
			},
			size: {
				type: [String, Number],
				default: '200'
			},
			timeout: {
				type: [String, Number],
				default: '0'
			},
			derive: {
				type: [String, Number],
				default: '100'
			},
			moveColor: {
				type: String,
				default: '#409EFF'
			},
			drag: {
				type: Boolean,
				default: false
			},
		},
		methods: {
			changeIsClick(newValue, oldValue, ownerInstance, instance) {
				this.Isclick = newValue
			}
		},
		data() {
			return {
				ismove: false,
				Isclick: Boolean
			}
		},
		created() {
			let _this = this
			let nums = uni.upx2px(1)
			let size = uni.upx2px(Number(_this.size))
			let startxt = {
				stare: `display: block;position: fixed;z-index: 999;width: ${nums}px;height: ${nums}px;background-color: ${_this.color};border-radius: 50px;opacity: 0.2;transform: scale(${_this.derive});transition: 0.3s transform;transform-origin: center;`,
				end: `display: block;position: fixed;z-index: 999;width: ${nums}px;height: ${nums}px;background-color: ${_this.color};border-radius: 50px;opacity: 0;transform: scale(${_this.derive});transition: 0.3s opacity;transform-origin: center;`
			}
			let linear = {
				stare: `display: block;position: fixed;z-index: 999;width: ${nums}px;height: ${nums}px;box-shadow: 0 0 0.1px ${_this.color} inset;border-radius: 50px;opacity: 0.8;transform: scale(${_this.derive});transition: 0.3s transform;transform-origin: center;`,
				end: `display: block;position: fixed;z-index: 999;width: ${nums}px;height: ${nums}px;box-shadow: 0 0 0.1px ${_this.color} inset;border-radius: 50px;opacity: 0;transform: scale(${_this.derive});transition: 0.3s opacity;transform-origin: center;`
			}
			let icons = {
				stare: `display: block;position: fixed;z-index: 999;width: ${size}px;height: ${size}px;background-image:url(${_this.icon});background-size:cover;`,
				end: `display: block;position: fixed;z-index: 999;width: ${size}px;height: ${size}px;background-image:url(${_this.icon});background-size:cover;opacity: 0;transition: 0.3s opacity;`
			}
			let moves = {
				stare: `display: block;position: fixed;z-index: 999;width: 5px;height: 5px;background-color: ${_this.moveColor};`,
				end: `display: block;position: fixed;z-index: 999;width: 5px;height: 5px;background-color: ${_this.moveColor};opacity: 0;transition: 0.3s opacity;`
			}
			document.body.onclick = function (e) {
				if (!_this.Isclick) {
					return
				}
				let creat = document.querySelector('.pc_clickEffect');
				let span = document.createElement('span');
				creat.appendChild(span);
				let clientX = e.clientX
				let clientY = e.clientY
				setTimeout(() => {
					if (_this.icon && _this.icon != '') {
						span.style = icons.stare + `left: ${clientX-Number(_this.size)/4}px;top: ${clientY-Number(_this.size)/4}px;`
					} else if (_this.model == 'default') {
						span.style = startxt.stare + `left: ${clientX}px;top: ${clientY}px;`
					} else if (_this.model == 'linear') {
						span.style = linear.stare + `left: ${clientX}px;top: ${clientY}px;`
					}
					setTimeout(() => {
						if (_this.icon && _this.icon != '') {
							span.style = icons.end + `left: ${clientX-Number(_this.size)/4}px;top: ${clientY-Number(_this.size)/4}px;`
						} else if (_this.model == 'default') {
							span.style = startxt.end + `left: ${clientX}px;top: ${clientY}px;`
						} else if (_this.model == 'linear') {
							span.style = linear.end + `left: ${clientX}px;top: ${clientY}px;`
						}
						setTimeout(() => {
							creat.removeChild(span)
						}, 500)
					}, _this.timeout)
				}, 10)
			}
			document.body.ontouchstart = function (e) {
				_this.ismove = _this.drag ? true : false;
			}
			document.body.ontouchmove = function (e) {
				if (_this.ismove) {
					let creat = document.querySelector('.pc_clickEffect');
					let span = document.createElement('span');
					creat.appendChild(span);
					let clientX = e.touches[0].clientX
					let clientY = e.touches[0].clientY
					span.style = moves.stare + `left: ${clientX}px;top: ${clientY}px;`
					setTimeout(() => {
						span.style = moves.end + `left: ${clientX}px;top: ${clientY}px;`
						setTimeout(() => {
							creat.removeChild(span)
						}, 500)
					}, _this.timeout)
				}
			}
			document.body.ontouchend = function (e) {
				_this.ismove = false;
			}
		}
	}
</script>