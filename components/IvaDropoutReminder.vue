<template>
	<view class="iva-dropout center" v-if="showDrop">
		<text class="iconfont" style="font-size: 60rpx;margin-right: 8rpx;">&#xe658;</text> 头环脱落请立即调整
	</view>
</template>

<script setup>
	import {
		onMounted,
		ref,
		watch,
		onUnmounted
	} from 'vue';
	import PopBoxVue from './PopBox.vue';
	import {
		useSocketStore
	} from '@/stores/socket';
	import {
		useHelper
	} from '@/stores/helper';
	import ws from '@/utils/websocket.js'
	import {
		wsUrl
	} from '../common/global';
	import {
		BleController
	} from '@/utils/bluType';
	import {
		getFakeTime,
		addRealEndTime,
		resetValue,
		getFakeTimeSW
	} from '@/utils/getFakeTime';
	import {
		useUserStore
	} from '../stores/user';
	const props = defineProps(['eceuId', 'isStartCollect'])
	const userStore = useUserStore()
	const helper = useHelper(); //设备仓库
	const socket = useSocketStore(); //websocket仓库
	const showDrop = ref(false)
	onMounted(() => {
		// #ifdef APP-PLUS
		helper.helperBlu && helper.helperBlu.changeListener(onMsg)
		ownBluInit()
		if (!socket.socketTask) {
			socket.socketTask = new ws(wsUrl, helper.address, userStore.outpatientId, userStore.userInfo.userId)
		}
		if (socket.socketTask && socket.socketTask.userClose) {
			socket.socketTask.reconnect(wsUrl, helper.address, userStore.outpatientId, userStore.userInfo.userId)
		}
		socket.socketTask.getWebSocketMsg((data) => {
			// console.log(data);
		})
		// #endif
	})
	onUnmounted(() => {
		//#ifdef APP-PLUS
		if (socket.socketTask) {
			resetValue()
			socket.socketTask.closeSocket()
		}
		//#endif
	})
	const ownBluInit = () => {
		BleController.addDeviceAcceptListListen(state => {
			// console.log('数据接受中', state);
			const ret = JSON.parse(state)
			helper.bluData = ret
			if (socket.socketTask && !socket.socketTask.userClose && props.isStartCollect && socket.socketTask.ws.readyState === 1) {
				socket.socketTask.webSocketSendMsg(JSON.stringify(getFakeTime(state, props.eceuId, 'ivacpt', socket.socketTask)))
			}
		})
	}

	watch(() => props.isStartCollect, (isStartCollect) => {
		if (!isStartCollect) {
			addRealEndTime(props.eceuId, 'ivacpt')
		}
	})
	const onMsg = (json) => {
		const ret = JSON.parse(json)
		let type = ret.type
		if (type == 1 || type == 7) {
			if (socket.socketTask && !socket.socketTask.userClose && props.isStartCollect && socket.socketTask.ws.readyState === 1) {
				socket.socketTask.webSocketSendMsg(JSON.stringify(getFakeTimeSW(json, props.eceuId, 'ivacpt')))
			}
			if (type == 1) {
				helper.bluData = ret
			}
		}
	}
	watch(() => helper.bluData, (bluData) => {
		if ((bluData.type == 5 || bluData.signal)) {
			showDrop.value = true
		}
		if (!bluData.signal) {
			showDrop.value = false
		}
	})
</script>

<style lang="scss">
	.iva-dropout {
		padding: 8rpx 16rpx 8rpx 8rpx;
		background: #FFFFFF;
		border-radius: 45rpx;
		position: absolute;
		top: 13%;
		left: 50%;
		transform: translateX(-50%);
		font-size: 41rpx;
		font-family: SourceHanSansCN, SourceHanSansCN;
		font-weight: 500;
		color: #FF4C4C;
		z-index: 2;
	}
</style>