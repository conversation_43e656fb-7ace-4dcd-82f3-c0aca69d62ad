import httpRequest from '@/utils/interceptors.js';



/**
 * @description: 记录脑电采集事件真实开始时间ivacpt
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const subEvalBeginTimeIva = (data) => {
	return httpRequest.post("ivacpt/v2.0/subEvalBeginTime", data);
};



/**
 * @description: 记录脑电采集事件真实结束时间ivacpt
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const subEvalEndTimeIva = (data) => {
	return httpRequest.post("ivacpt/v2.0/subEvalEndTime", data);
};