import httpRequest from '@/utils/interceptors.js';

/**
 * @description: 新增工单
 * @author: 许江涛
 * @param {{ traineeId: string, projectIds: string[], assessmentSetId?: string|number }} data
 * @return {*}
 */
export const addWorkOrder = (data = {}) => {
  return httpRequest.post('workorder/v1.1/addWorkOrder', data);
};


/**
 * @description: 查询工单列表（qryWorkOrderListPage）
 * @author: 许江涛
 * @param {{
 *   param: { param: string },
 *   pageSize: number,
 *   pageIndex: number,
 *   evaluationSourceType?: 0|1,
 *   state?: 'TODO'|'DOING'|'DONE'|'CANCELED',
 *   beginDate?: number, // 13位时间戳（开始时间 00:00:00.000）
 *   endDate?: number // 13位时间戳（结束时间 23:59:59.999）
 * }} data
 * @return {*}
 */
export const qryWorkOrderListPage = (data = { param: { param: '' }, pageSize: 10, pageIndex: 1 }) => {
  return httpRequest.post('workorder/v1.1/qryWorkOrderListPage', data);
};

/**
 * @description: 更新工单状态
 * @author: 许江涛
 * @param {{ workOrderId: string, state: 'TODO'|'DOING'|'DONE'|'CANCELED' }} data
 * @return {*}
 */
export const updateWorkOrderState = (data = {}) => {
  return httpRequest.post('workorder/v1.1/updateWorkOrderState', data);
};

/**
 * @description: 重新提交工单
 * @author: 许江涛
 * @param {{ workorderId: string }} data
 * @return {*}
 */
export const reSubmitWorkOrder = (data = {}) => {
  return httpRequest.post('workorder/v1.1/reSubmitWorkOrder', data);
};

/**
 * @description: 查询工单详情信息
 * @author: 许江涛
 * @param {{ workOrderId: string }} data
 * @return {*}
 */
export const qryWorkOrderDetailInfo = (data = {}) => {
  return httpRequest.post('workorder/v1.1/qryWorkOrderDetailInfo', data);
};

/**
 * @description: 生成工单二维码
 * @author: 许江涛
 * @param {{ workOrderId: string, respondentGroup: 'P'|'T' }} data
 * @return {*}
 */
export const generalWorkOrderQrCode = (data = {}) => {
  return httpRequest.post('workorder/v1.1/generalWorkOrderQrCode', data);
};

/**
 * @description: 查询工单二维码信息
 * @author: 许江涛
 * @param {{ qrCodeId: string }} data
 * @return {*}
 */
export const qryWorkOrderQrCodeInfo = (data = {}) => {
  return httpRequest.post('workorder/v1.1/qryWorkOrderQrCodeInfo', data);
};


