/*
 * @Description:主页面请求
 * @Author: 小雨
 * @Date: 2023-03-08 09:18:05
 * @LastEditTime: 2023-03-08 13:28:19
 * @LastEditors: 小雨
 */
import httpRequest from '@/utils/interceptors.js';

/**
 * @description:新增患者信息
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const addPPVTUserInfo = (data) => {
	return httpRequest.post('ppvt/patient/v1.1/addTraineeInfo', data);
};

/**
 * @description:更新患者信息
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const updatePPVTUserInfo = (data) => {
	return httpRequest.post('ppvt/patient/v1.1/updateTraineeInfo', data);
};


/**
 * @description:新增pptv免费测评信息
 * @author: 小雨
 * @return {*}
 * @param {*} data phoneNumber
 */
export const addFreeEvaInfo = (data) => {
	return httpRequest.post('ppvtEvaluation/1.0/addFreeEvaInfo', data);
};

/**
 * @description:查询pptv免费测评结果pdf
 * @author: 小雨
 * @return {*}
 * @param {*} data phoneNumber
 */
export const qryFreeEvaResultPDF = (data) => {
	return httpRequest.get('ppvtEvaluation/1.0/qryFreeEvaResultPDF', data);
};

/**
 * @description:查询pptv免费测评列表 2.0
 * @author: 小雨
 * @return {*}
 * @param {*} data phoneNumber
 */
export const qryFreeEvaInfoList = (data) => {
	return httpRequest.post('ppvtEvaluation/2.0/qryFreeEvaInfoList', data);
};

/**
 * @description:查询pptv免费测评列表 1.0
 * @author: 小雨
 * @return {*}
 * @param {*} data phoneNumber
 */
export const getQryFreeEvaInfoList = (data) => {
	return httpRequest.post('ppvtEvaluation/1.0/qryFreeEvaInfoList', data);
};
/**
 * @description:查询pptv用户信息
 * @author: 小雨
 * @return {*}
 * @param {*} data traineeId
 */
export const qryPPVTUserInfo = (data) => {
	return httpRequest.post('ppvt/patient/v1.1/qryTraineeDetail', data).then(res => {
		// 适配新接口返回格式到旧格式
		if (res.data) {
			const adaptedData = {
				serNum: res.data.medicalRecordNo,
				outpatientId: res.data.traineeId,
				userName: res.data.traineeName,
				sex: res.data.sex === 'M' ? 1 : res.data.sex === 'F' ? 2 : -1, // 将 'M'/'F' 转换为 1/2
				birth: res.data.birth ? res.data.birth.substring(0, 10) : '', // 只取 YYYY-MM-dd 部分
				stanOfCul: res.data.stanOfCul,
				grade: res.data.grade || res.data.stanOfCul || '1', // 优先使用接口返回的grade字段，其次使用stanOfCul，最后使用默认值
				remark: res.data.desc,
				canPPvt: res.data.canPPvt !== undefined ? res.data.canPPvt : true
			};
			res.data = adaptedData;
		}
		return res;
	});
};

/**
 * @description:校验患者信息（checkPPVTPatient）（修改）
 * @author: 小雨
 * @return {*}
 * @param {*} data outpatientId
 */
export const checkPPVTUser = (data) => {
	return httpRequest.post('ppvt/patient/2.0/checkPPVTPatient', data);
};



/**
 * @description:PPVT登录（login）（新增）
 * @author: 小雨
 * @return {*}
 * @param {*} data outpatientId
 */
export const login = (data) => {
	return httpRequest.post('ppvt/user/v1.1/login', data);
};

/**
 * @description:获取当前登录信息
 * @author: 小雨
 * @return {*}
 * @param {*} data outpatientId
 */
export const getUserInfo = (data) => {
	return httpRequest.post('ppvt/user/1.0/getUserInfo', data);
};


/**
 * @description:查询患者列表（qryPatientListPage）（新增
 * @author: 小雨
 * @return {*}
 * @param {*} data outpatientId
 */
export const qryPatientListPage = (data) => {
	return httpRequest.post('ppvt/patient/2.0/qryPatientListPage', data);
};

/**
 * @description:查询学员列表（qryTraineeInfoListPage）
 * @author: 小雨
 * @return {*}
 * @param {*} data outpatientId
 */
export const qryTraineeInfoListPage = (data) => {
	return httpRequest.post('ppvt/patient/v1.1/qryTraineeInfoListPage', data);
};

/**
 * @description: 查询学员简要列表（qryTraineeList）
 * @author: 许江涛
 * @param {*} data { param: string }
 * @return {*}
 */
export const qryTraineeList = (data) => {
    return httpRequest.post('ppvt/patient/v1.1/qryTraineeList', data);
};