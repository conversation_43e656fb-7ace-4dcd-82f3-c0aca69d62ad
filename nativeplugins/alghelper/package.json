{"name": "dbay-sdk-uniapp扩展", "id": "<PERSON><PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "深湾脑波设备SDK，uniapp原生插件", "_dp_type": "nativeplugin", "_dp_nativeplugin": {"android": {"plugins": [{"type": "module", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "com.deepbaysz.alg.BLHelper"}], "hooksClass": "", "integrateType": "aar", "dependencies": [], "compileOptions": {"sourceCompatibility": "1.8", "targetCompatibility": "1.8"}, "abis": ["arm64-v8a", "armeabi-v7a"], "minSdkVersion": "22", "permissions": [], "parameters": {}}, "ios": {"plugins": [{"type": "module", "name": "<PERSON><PERSON><PERSON><PERSON>", "class": "BLHelper"}], "hooksClass": "", "integrateType": "framework", "deploymentTarget": "10.0", "frameworks": [], "embedFrameworks": [], "privacies": ["Bluetooth Always Usage Description"]}}}