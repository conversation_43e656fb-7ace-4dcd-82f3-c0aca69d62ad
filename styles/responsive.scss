/**
 * 响应式适配样式
 * <AUTHOR>
 * @description 基于CSS变量的响应式适配样式系统
 */

// 响应式断点定义
$breakpoints: (
  'small': 'screen and (max-width: 1200px) and (max-height: 800px)',
  'medium': 'screen and (min-width: 1201px) and (max-width: 1600px)',
  'large': 'screen and (min-width: 1601px)',
  'high-dpi': 'screen and (-webkit-min-device-pixel-ratio: 2), screen and (min-resolution: 192dpi)',
  'small-screen': 'screen and (max-width: 1400px) and (max-height: 900px)', // 小屏幕设备
  'large-screen': 'screen and (min-width: 1920px)',  // 大屏幕设备
  '11inch-screen': 'screen and (min-width: 1800px) and (max-width: 2000px) and (min-height: 1100px) and (max-height: 1300px)' // 11英寸高分辨率屏幕
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media #{map-get($breakpoints, $breakpoint)} {
      @content;
    }
  } @else {
    @warn "Breakpoint #{$breakpoint} not found in $breakpoints.";
  }
}

// 自适应字体大小混入
@mixin adaptive-font-size($base-size: 16px, $min-size: 12px, $max-size: 24px) {
  font-size: clamp(#{$min-size}, #{$base-size} * var(--responsive-scale, 1), #{$max-size});
}

// 自适应间距混入
@mixin adaptive-spacing($property, $base-value: 16px, $min-value: 8px, $max-value: 32px) {
  #{$property}: clamp(#{$min-value}, #{$base-value} * var(--responsive-scale, 1), #{$max-value});
}

// 自适应尺寸混入
@mixin adaptive-size($width: auto, $height: auto, $min-width: auto, $max-width: auto) {
  @if $width != auto {
    width: calc(#{$width} * var(--responsive-scale, 1));
  }
  @if $height != auto {
    height: calc(#{$height} * var(--responsive-scale, 1));
  }
  @if $min-width != auto {
    min-width: calc(#{$min-width} * var(--responsive-scale, 1));
  }
  @if $max-width != auto {
    max-width: calc(#{$max-width} * var(--responsive-scale, 1));
  }
}

// 全局响应式类
.responsive-container {
  // 小屏幕适配
  @include respond-to('small') {
    --responsive-scale: 0.8;
    
    .table-cell {
      @include adaptive-font-size(18px, 14px, 20px);
      @include adaptive-spacing(padding, 6px, 4px, 12px);
    }
    
    .header .title {
      @include adaptive-font-size(24px, 20px, 28px);
    }
    
    .filter-header {
      @include adaptive-spacing(gap, 16px, 12px, 24px);
      @include adaptive-spacing(padding, 12px, 8px, 20px);
      
      .search-input {
        @include adaptive-font-size(18px, 16px, 22px);
        min-width: calc(200px * var(--responsive-scale, 1));
      }
      
      .filter-select {
        width: calc(160px * var(--responsive-scale, 1));
      }
    }
    
    .btn-new-order .btn-text {
      @include adaptive-font-size(16px, 14px, 18px);
    }
    
    .pagination {
      .total-text,
      .page-size-text,
      .goto-text {
        @include adaptive-font-size(18px, 16px, 20px);
      }
      
      .page-number {
        @include adaptive-size(30px, 30px);
        @include adaptive-font-size(18px, 16px, 20px);
      }
    }
  }
  
  // 高DPI设备适配
  @include respond-to('high-dpi') {
    --responsive-scale: 0.85;
    
    .table-header .header-cell,
    .table-cell {
      border-width: 0.5px; // 高DPI设备使用更细的边框
    }
  }
  
  // 大屏幕适配
  @include respond-to('large') {
    --responsive-scale: 1.1;
    
    .assessment-page {
      max-width: 1800px;
      margin: 0 auto;
    }
  }
}

// 表格响应式适配
.responsive-table {
  .table-header,
  .table-row {
    display: flex;
    
    // 小屏幕下调整列宽比例
    @include respond-to('small') {
      .col-index { width: 5%; }
      .col-patient { width: 25%; }
      .col-order { width: 22%; }
      .col-time { width: 20%; }
      .col-status { width: 12%; }
      .col-action { width: 16%; }
    }
  }
  
  .table-cell {
    @include adaptive-font-size(22px, 16px, 26px);
    @include adaptive-spacing(padding, 8px, 6px, 12px);
    min-height: calc(61px * var(--responsive-scale, 1));
    
    // 小屏幕下的特殊处理
    @include respond-to('small') {
      &.col-patient .patient-info .patient-name {
        @include adaptive-font-size(20px, 16px, 22px);
      }
      
      &.col-patient .patient-info .patient-details {
        @include adaptive-font-size(18px, 14px, 20px);
      }
    }
  }
}

// 按钮响应式适配
.responsive-button {
  @include adaptive-spacing(padding, 15px 22px, 10px 16px, 20px 28px);
  @include adaptive-font-size(22px, 16px, 26px);
  border-radius: calc(7px * var(--responsive-scale, 1));
  
  @include respond-to('small') {
    @include adaptive-spacing(padding, 12px 18px, 8px 14px, 16px 22px);
    @include adaptive-font-size(18px, 16px, 20px);
  }
}

// 输入框响应式适配
.responsive-input {
  @include adaptive-size(auto, 44px);
  @include adaptive-font-size(22px, 16px, 26px);
  @include adaptive-spacing(padding-left, 15px, 10px, 20px);
  border-radius: calc(7px * var(--responsive-scale, 1));
  
  &::placeholder {
    @include adaptive-font-size(22px, 16px, 26px);
  }
  
  @include respond-to('small') {
    @include adaptive-size(auto, 36px);
    @include adaptive-font-size(18px, 16px, 20px);
    
    &::placeholder {
      @include adaptive-font-size(18px, 16px, 20px);
    }
  }
}

// 状态标签响应式适配
.responsive-status-tag {
  @include adaptive-spacing(padding, 9px 11px, 6px 8px, 12px 14px);
  @include adaptive-font-size(20px, 16px, 22px);
  border-radius: calc(4px * var(--responsive-scale, 1));
  
  @include respond-to('small') {
    @include adaptive-spacing(padding, 6px 8px, 4px 6px, 8px 10px);
    @include adaptive-font-size(16px, 14px, 18px);
  }
}

// 分页组件响应式适配
.responsive-pagination {
  @include adaptive-spacing(padding, 11px 15px, 8px 12px, 14px 18px);
  
  .pagination-controls {
    @include adaptive-spacing(gap, 7px, 5px, 10px);
    
    .page-btn,
    .page-number {
      @include adaptive-size(37px, 37px);
      @include adaptive-font-size(22px, 16px, 26px);
      border-radius: calc(4px * var(--responsive-scale, 1));
      
      @include respond-to('small') {
        @include adaptive-size(30px, 30px);
        @include adaptive-font-size(18px, 16px, 20px);
      }
    }
  }
  
  .goto-input {
    @include adaptive-size(37px, 37px);
    border-radius: calc(4px * var(--responsive-scale, 1));
    
    .goto-input-field {
      @include adaptive-font-size(22px, 16px, 26px);
    }
    
    @include respond-to('small') {
      @include adaptive-size(30px, 30px);
      
      .goto-input-field {
        @include adaptive-font-size(18px, 16px, 20px);
      }
    }
  }
}

// 浮窗日历响应式适配
.responsive-date-popover {
  @include adaptive-size(280px);
  @include adaptive-spacing(padding, 10px 10px 14px);
  border-radius: calc(8px * var(--responsive-scale, 1));
  
  .ym-text {
    @include adaptive-font-size(16px, 14px, 18px);
  }
  
  .week-cell {
    @include adaptive-font-size(12px, 10px, 14px);
  }
  
  .day-cell {
    @include adaptive-size(auto, 34px);
    @include adaptive-font-size(14px, 12px, 16px);
    border-radius: calc(4px * var(--responsive-scale, 1));
  }
  
  .nav-btn {
    @include adaptive-size(28px, 28px);
    border-radius: calc(4px * var(--responsive-scale, 1));
  }
  
  @include respond-to('small') {
    @include adaptive-size(240px);
    @include adaptive-spacing(padding, 8px 8px 12px);
    
    .day-cell {
      @include adaptive-size(auto, 28px);
    }
    
    .nav-btn {
      @include adaptive-size(24px, 24px);
    }
  }
}

// 11英寸屏幕专用优化
html.screen-11inch .responsive-container,
.responsive-container {
  @include respond-to('11inch-screen') {
    --responsive-scale: 0.85; // 调整缩放比例，避免过度缩小

    // 过滤条件行优化 - 缩小字体和间距
    .filter-header {
      gap: 16px; // 减少间距从22px到16px
      padding: 12px 18px; // 减少内边距

      .search-frame {
        .search-input {
          min-width: 240px; // 缩小搜索框宽度
          font-size: 20px; // 缩小字体
          height: 40px; // 缩小高度

          &::placeholder {
            font-size: 20px;
          }
        }

        .search-addon {
          width: 40px;
          height: 40px;
        }
      }

      .filter-select {
        width: 180px; // 缩小下拉框宽度
        padding: 10px 14px; // 减少内边距

        .filter-text {
          font-size: 20px; // 缩小字体
        }

        .dropdown .dropdown-item {
          font-size: 18px; // 下拉项字体
          padding: 8px 10px;
        }
      }

      .date-picker {
        padding: 10px 14px; // 减少内边距

        .date-text {
          font-size: 20px; // 缩小字体
        }
      }

      .button-group-right {
        gap: 10px; // 减少按钮间距

        .btn-search,
        .btn-reset {
          padding: 12px 18px; // 减少按钮内边距
          font-size: 20px; // 缩小字体
        }
      }
    }
    
    // 表格优化 - 调整列宽比例和字体大小
    .responsive-table {
      // 表头高度优化 - 减半
      .table-header {
        min-height: var(--table-header-height) !important;

        .header-cell {
          min-height: var(--table-header-height) !important;
        }
      }

      .table-header,
      .table-row {
        .col-index { width: 4%; }
        .col-patient { width: 20%; } // 缩小患者信息列
        .col-order { width: 18%; } // 缩小测评工单列
        .col-time { width: 18%; } // 缩小派单时间列
        .col-status { width: 18%; } // 增大状态列宽度以容纳垂直布局
        .col-action { width: 22%; } // 增大操作列宽度
      }

      .table-cell {
        font-size: 20px; // 缩小表格字体但保持可读性
        padding: 8px 10px; // 减少单元格内边距
        min-height: 52px; // 调整行高

        &.col-patient {
          .patient-info {
            .patient-name {
              font-size: 20px;
              line-height: 1.2;
            }

            .patient-details {
              font-size: 18px;
              line-height: 1.2;
            }
          }
        }
      }

      .table-header .header-cell {
        font-size: 20px; // 缩小表头字体
        padding: 6px 10px; // 减半内边距：从12px减到6px
      }
    }
    
    // 状态标签优化
    .responsive-status-tag {
      padding: 8px 10px; // 适度减少内边距
      font-size: 18px; // 缩小字体但保持可读性
    }

    // 状态列垂直布局优化 - 11英寸屏幕专用
    .col-status {
      align-items: flex-start !important; // 改为顶部对齐，适配垂直布局
      justify-content: flex-start !important;
      padding-left: 14px !important; // 调整左边距

      .status-wrapper {
        display: flex !important;
        flex-direction: column !important; // 改为垂直排列
        align-items: flex-start !important; // 左对齐
        gap: 6px !important; // 适度的垂直间距
        width: 100%;

        .status-tag,
        .progress-tag {
          width: auto;
          white-space: nowrap; // 防止标签内文字换行
          flex-shrink: 0; // 防止标签被压缩
          font-size: 18px; // 确保字体大小一致
        }
      }
    }

    // 操作按钮优化
    .action-btns {
      .action-btn {
        .btn-text {
          font-size: 20px; // 缩小操作按钮字体但保持可读性
        }

        .btn-divider {
          height: 14px; // 调整分隔线高度
        }
      }
    }
    
    // 分页组件优化
    .responsive-pagination {
      padding: 10px 14px; // 适度减少内边距

      .total-text,
      .page-size-text,
      .goto-text {
        font-size: 20px; // 缩小字体但保持可读性
      }

      .pagination-controls {
        gap: 6px; // 适度减少间距

        .page-btn,
        .page-number {
          width: 32px;
          height: 32px;
          font-size: 20px;
        }
      }

      .goto-input {
        width: 32px;
        height: 32px;

        .goto-input-field {
          font-size: 20px;
        }
      }
    }

    // 页面标题优化 - 高度减半
    .header {
      height: var(--header-height) !important; // 使用CSS变量动态设置高度

      .title {
        font-size: 26px; // 适度缩小标题字体
        top: 50% !important; // 垂直居中
        transform: translateX(-50%) translateY(-50%) !important; // 完全居中
      }

      .back-btn {
        top: 50% !important; // 垂直居中
        transform: translateY(-50%) !important; // 垂直居中
      }

      .button-group {
        top: 50% !important; // 垂直居中
        transform: translateY(-50%) !important; // 垂直居中
      }
    }

    // 新增工单按钮优化
    .btn-new-order {
      .btn-text {
        font-size: 18px; // 适度缩小按钮文字
      }

      .btn-icon {
        width: 17px;
        height: 17px;
      }
    }

    // 测评记录页面专用优化
    .assessment-record-page {
      // 头部区域高度减半
      .header {
        height: var(--header-height-assessment-record) !important; // 从88px减半到44px

        .title {
          top: 50% !important; // 垂直居中
          transform: translateX(-50%) translateY(-50%) !important; // 完全居中
          font-size: 26px; // 缩小字体
        }

        .back-btn {
          top: 50% !important; // 垂直居中
          transform: translateY(-50%) !important; // 垂直居中
        }
      }

      // 筛选区域优化
      .filter-section {
        padding: 20px; // 减少内边距
        margin-bottom: 12px; // 减少底部间距
        gap: 15px; // 减少网格间距

        // 调整网格列宽
        grid-template-columns: 1fr 180px 200px 480px 100px 200px;

        .search-frame .search-input {
          height: 40px; // 缩小高度
          font-size: 20px; // 缩小字体

          &::placeholder {
            font-size: 20px;
          }
        }

        .search-addon {
          width: 40px;
          height: 40px;
        }

        .filter-select {
          padding: 10px 14px; // 减少内边距

          .filter-text {
            font-size: 20px; // 缩小字体
          }

          .dropdown .dropdown-item {
            font-size: 18px;
            padding: 8px 10px;
          }
        }

        .date-picker {
          padding: 10px 14px;

          .date-text {
            font-size: 20px;
          }
        }

        .score-range,
        .age-range {
          gap: 15px; // 减少间距

          .score-input,
          .age-input {
            height: 40px; // 缩小高度

            .score-field,
            .age-field {
              height: 40px;
              font-size: 20px;

              &::placeholder {
                font-size: 20px;
              }
            }
          }

          .range-separator {
            font-size: 20px;
          }
        }

        .button-group {
          gap: 15px;

          .btn-reset,
          .btn-search {
            height: 40px;
            font-size: 20px;
            padding: 0 20px;
          }
        }
      }

      // 表格区域优化
      .table-section {
        .table-title {
          padding: 16px 20px; // 减少内边距

          .title-text {
            font-size: 20px; // 缩小字体
          }
        }
      }

      // 表头高度减半优化
      .table-header {
        min-height: var(--table-header-height) !important;

        .header-cell {
          min-height: var(--table-header-height) !important;
        }
      }

      // 表格单元格优化
      .table-cell {
        font-size: 20px; // 缩小字体
        padding: 8px 10px; // 减少内边距
        min-height: 52px; // 调整行高
      }

      .table-header .header-cell {
        font-size: 20px; // 缩小表头字体
        padding: 6px 10px; // 减半内边距：从16px减到8px，再到6px
      }

      // 分页优化
      .pagination {
        padding: 10px 14px;

        .total-text {
          font-size: 20px;
        }

        .page-size-text,
        .goto-text {
          font-size: 20px;
        }

        .page-number {
          width: 28px;
          height: 28px;
          font-size: 20px;
        }

        .page-btn {
          width: 28px;
          height: 28px;
        }

        .goto-input {
          width: 36px;
          height: 28px;

          .goto-input-field {
            font-size: 20px;
          }
        }
      }
    }
  }
}

// 工具类
.text-responsive {
  @include adaptive-font-size(16px, 12px, 20px);
}

.text-responsive-sm {
  @include adaptive-font-size(14px, 12px, 16px);
}

.text-responsive-lg {
  @include adaptive-font-size(20px, 16px, 24px);
}

.text-responsive-xl {
  @include adaptive-font-size(24px, 18px, 30px);
}

.spacing-responsive {
  @include adaptive-spacing(margin, 16px, 8px, 24px);
  @include adaptive-spacing(padding, 16px, 8px, 24px);
}

.spacing-responsive-sm {
  @include adaptive-spacing(margin, 8px, 4px, 12px);
  @include adaptive-spacing(padding, 8px, 4px, 12px);
}

.spacing-responsive-lg {
  @include adaptive-spacing(margin, 24px, 16px, 32px);
  @include adaptive-spacing(padding, 24px, 16px, 32px);
}
