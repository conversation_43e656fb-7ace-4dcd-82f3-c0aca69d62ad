/*
 * @Description: 统一提示组件方便全局修改
 * @Author: 小雨
 * @Date: 2023-03-08 09:14:08
 * @LastEditTime: 2023-03-08 13:25:05
 * @LastEditors: 小雨
 */
//统一提示组件方便全局修改
export const msg = (title, duration = 1500, mask = false, icon = 'none') => {
	if (Boolean(title) === false) {
		return;
	}
	uni.showToast({
		title,
		duration,
		mask,
		icon,
	});
};

export const hideLoading = () => {
	return uni.hideLoading();
};

export const getStorageSync = (value) => {
	return uni.getStorageSync(value);
};

export const setNavigationBarTitle = (value) => {
	return uni.setNavigationBarTitle({
		title: value,
	});
};

export const navigateTo = (value) => {
	// 跳转到目的页面，打开新页面
	return uni.navigateTo({
		url: value,
	});
};
export const switchTab = (value) => {
	// 跳转到 tabBar 页面，并关闭其他所有非 tabBar 页面
	return uni.switchTab({
		url: value,
	});
};
export const navigateBack = (value) => {
	// 跳转到目的页面，打开新页面
	return uni.navigateBack({
		delta: value,
	});
};
export const redirectTo = (value) => {
	//  跳转到目的页面，在当前页面打开
	return uni.redirectTo({
		url: value,
	});
};

export const setStorage = (key, value) => {
	return uni.setStorage({
		key: key,
		data: value,
	});
};
export const clearStorage = (key, value) => {
	return uni.clearStorage();
};

export const clearStorageSync = (key, value) => {
	return uni.clearStorageSync();
};
export const showToast = (text, time) => {
	return uni.showToast({
		title: text,
		duration: time ? time : 1000,
		icon: 'none',
	});
};
// 保持屏幕常亮
export const setKeepScreenOn = () => {
	return uni.setKeepScreenOn({
		keepScreenOn: true,
		success: () => {
			console.log('保持常亮');
		},
		fail: () => {
			console.log('常亮失败');
		},
	});
};

export const getEnv = () => {
	return uni.getEnv();
};

export const reLaunch = (path) => {
	return uni.reLaunch({
		url: path,
	});
};

export const showLoading = (value) => {
	return uni.showLoading({
		title: value,
	});
};

export const hideShareMenu = () => {
	return uni.hideShareMenu();
};