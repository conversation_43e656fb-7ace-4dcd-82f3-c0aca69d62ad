/*
 * @Description:
 * @Author: 小雨
 * @Date: 2022-08-11 10:08:56
 * @LastEditTime: 2022-11-16 14:43:36
 * @LastEditors: 小雨
 */

//随机1~9随机数
export const ram = () => Math.ceil((Math.random() * 1000000000) % 9);

//随机长度为range的数组范围1~length数组不重复
export const rangeRam = (range) => {
	const ramArr = [];
	while (ramArr.length < range) {
		let r = ram();
		if (ramArr.indexOf(r) === -1) {
			ramArr.push(r);
		}
	}
	return ramArr;
};
//随机长度为range的数组范围1~length数组不重复
export const rangeArrRam = (range, value) => {
	const ramArr = [];
	while (ramArr.length < range) {
		let r = value;
		if (ramArr.indexOf(r) === -1) {
			ramArr.push(r);
		}
	}
	return ramArr;
};
//生成从minNum到maxNum的随机数
export function randomNum(minNum, maxNum) {
	switch (arguments.length) {
		case 1:
			return parseInt(Math.random() * minNum + 1, 10);
			break;
		case 2:
			return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
			break;
		default:
			return 0;
			break;
	}
}

//js实现将秒数格式化为HH:MM:SS的形式
export function formatSeconds(value, isHour = true) {
	let secondTime = parseInt(value);
	let minuteTime = 0;
	let hourTime = 0;
	if (secondTime >= 60) {
		minuteTime = parseInt(secondTime / 60);
		secondTime = parseInt(secondTime % 60);
		if (minuteTime >= 60) {
			hourTime = parseInt(minuteTime / 60);
			minuteTime = parseInt(minuteTime % 60);
		}
	}
	// 补0
	hourTime = hourTime < 10 ? "0" + hourTime : hourTime;
	minuteTime = minuteTime < 10 ? "0" + minuteTime : minuteTime;
	secondTime = secondTime < 10 ? "0" + secondTime : secondTime;
	let res = hourTime + ":" + minuteTime + ":" + secondTime;
	if (!isHour) {
		res = minuteTime + ":" + secondTime;
	}
	return res;
}

//  秒数转化为时分秒
export function formatTextSeconds(value) {
	//  秒
	let second = parseInt(value);
	//  分
	let minute = 0;
	//  小时
	let hour = 0;
	//  天
	//  let day = 0
	//  如果秒数大于60，将秒数转换成整数
	if (second > 60) {
		//  获取分钟，除以60取整数，得到整数分钟
		minute = parseInt(second / 60);
		//  获取秒数，秒数取佘，得到整数秒数
		second = parseInt(second % 60);
		//  如果分钟大于60，将分钟转换成小时
		if (minute > 60) {
			//  获取小时，获取分钟除以60，得到整数小时
			hour = parseInt(minute / 60);
			//  获取小时后取佘的分，获取分钟除以60取佘的分
			minute = parseInt(minute % 60);
			//  如果小时大于24，将小时转换成天
			//  if (hour > 23) {
			//    //  获取天数，获取小时除以24，得到整天数
			//    day = parseInt(hour / 24)
			//    //  获取天数后取余的小时，获取小时除以24取余的小时
			//    hour = parseInt(hour % 24)
			//  }
		}
	}

	let result = "" + parseInt(second) + "秒";
	if (minute > 0) {
		result = "" + parseInt(minute) + "分" + result;
	}
	if (hour > 0) {
		result = "" + parseInt(hour) + "小时" + result;
	}
	//  if (day > 0) {
	//    result = '' + parseInt(day) + '天' + result
	//  }
	return result;
}

// 获取页面URL参数
export function getLocationParams(name) {
	//获取页面栈
	const pages = getCurrentPages();
	//获取路由参数
	const curPage = pages[pages.length - 1];
	return name ? curPage.options[name] : curPage.options;
}


// 判断字符串是否为JSON格式
export function isJSON(str) {
	if (typeof str == 'string') {
		try {
			var obj = JSON.parse(str);
			if (typeof obj == 'object' && obj) {
				return true;
			} else {
				return false;
			}

		} catch (e) {
			// console.log('error：'+str+'!!!'+e);
			return false;
		}
	}
	// console.log('It is not a string!')
}

//是否为手机号
export function isMobile(mobile) {
	return /^1[3,4,5,6,7,8,9][0-9]{9}$/.test(mobile)
}

//患者关系
export function getRela(rela) {
	switch (rela) {
		case 1:
			return '父子'
			break;
		case 2:
			return '父女'
			break;
		case 3:
			return '母子'
			break;
		case 4:
			return '母女'
			break;
		case 5:
			return '祖孙'
			break;
		case 6:
			return '其他'
			break;
		default:
			return '本人'
			break;
	}
}

// 标准时间转换成年月日时分秒（补0）
export function getTime(timestamp) {
	let date = new Date(timestamp); //时间戳为10位需*1000，时间戳为13位的话不需乘1000
	let Y = date.getFullYear(),
		M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1),
		D = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate())
	return Y + '-' + M + '-' + D
}


//患者关系
export function getSch(sch) {
	switch (sch) {
		case "1":
			return '幼儿园'
		case "2":
			return '小学'
		case "3":
			return '初中'
		case "4":
			return '高中'
	}
}


export function hex2int(hex) {
	let len = hex.length,
		a = new Array(len),
		code;
	for (let i = 0; i < len; i++) {
		code = hex.charCodeAt(i);
		if (48 <= code && code < 58) {
			code -= 48;
		} else {
			code = (code & 0xdf) - 65 + 10;
		}
		a[i] = code;
	}

	return a.reduce(function (acc, c) {
		acc = 16 * acc + c;
		return acc;
	}, 0);
}