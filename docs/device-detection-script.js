/**
 * 设备检测验证脚本
 * 在浏览器控制台中运行此脚本，快速检查设备信息和11英寸屏幕检测结果
 */

(function() {
    console.log('=== 设备检测验证脚本 ===');
    
    // 获取基础设备信息
    const deviceInfo = {
        windowWidth: window.innerWidth,
        windowHeight: window.innerHeight,
        pixelRatio: window.devicePixelRatio || 1,
        screenWidth: window.screen.width,
        screenHeight: window.screen.height
    };
    
    // 计算物理像素
    const physicalPixelWidth = deviceInfo.windowWidth * deviceInfo.pixelRatio;
    const physicalPixelHeight = deviceInfo.windowHeight * deviceInfo.pixelRatio;
    
    // 计算物理尺寸（英寸）
    const physicalWidth = physicalPixelWidth / (96 * deviceInfo.pixelRatio);
    const physicalHeight = physicalPixelHeight / (96 * deviceInfo.pixelRatio);
    const diagonalInches = Math.sqrt(physicalWidth * physicalWidth + physicalHeight * physicalHeight);
    
    // 11英寸屏幕检测逻辑
    function is11InchScreen(deviceInfo) {
        const { windowWidth, windowHeight, pixelRatio } = deviceInfo;

        // 计算物理分辨率（实际像素）
        const physicalPixelWidth = windowWidth * pixelRatio;
        const physicalPixelHeight = windowHeight * pixelRatio;

        // 检测目标物理分辨率：1920×1200
        const isTargetPhysicalResolution = 
            Math.abs(physicalPixelWidth - 1920) <= 10 && 
            Math.abs(physicalPixelHeight - 1200) <= 10;

        // 检测逻辑分辨率范围：约1097×670 (允许一定误差)
        const isTargetLogicalResolution = 
            windowWidth >= 1000 && windowWidth <= 1200 &&
            windowHeight >= 600 && windowHeight <= 750;

        // 检测像素密度比：约1.75 (允许1.5-2.0范围)
        const isTargetPixelRatio = pixelRatio >= 1.5 && pixelRatio <= 2.0;

        // 计算物理尺寸（英寸）- 基于物理像素
        const physicalWidth = physicalPixelWidth / (96 * pixelRatio);
        const physicalHeight = physicalPixelHeight / (96 * pixelRatio);
        const diagonalInches = Math.sqrt(physicalWidth * physicalWidth + physicalHeight * physicalHeight);

        // 11英寸屏幕的对角线尺寸范围
        const is11InchSize = diagonalInches >= 10.5 && diagonalInches <= 12.5;

        // 主要检测：物理分辨率 + 像素密度比
        const primaryDetection = isTargetPhysicalResolution && isTargetPixelRatio;

        // 备用检测：逻辑分辨率 + 像素密度比 + 物理尺寸
        const fallbackDetection = isTargetLogicalResolution && isTargetPixelRatio && is11InchSize;

        const result = primaryDetection || fallbackDetection;

        return {
            result,
            details: {
                physicalPixelWidth,
                physicalPixelHeight,
                physicalWidth: physicalWidth.toFixed(2),
                physicalHeight: physicalHeight.toFixed(2),
                diagonalInches: diagonalInches.toFixed(2),
                isTargetPhysicalResolution,
                isTargetLogicalResolution,
                isTargetPixelRatio,
                is11InchSize,
                primaryDetection,
                fallbackDetection
            }
        };
    }
    
    // 执行检测
    const detection = is11InchScreen(deviceInfo);
    
    // 输出结果
    console.log('\n📱 基础设备信息:');
    console.table({
        '逻辑像素宽度': deviceInfo.windowWidth,
        '逻辑像素高度': deviceInfo.windowHeight,
        '像素密度比': deviceInfo.pixelRatio,
        '屏幕宽度': deviceInfo.screenWidth,
        '屏幕高度': deviceInfo.screenHeight
    });
    
    console.log('\n🔍 计算结果:');
    console.table({
        '物理像素宽度': Math.round(physicalPixelWidth),
        '物理像素高度': Math.round(physicalPixelHeight),
        '物理宽度(英寸)': physicalWidth.toFixed(2),
        '物理高度(英寸)': physicalHeight.toFixed(2),
        '对角线尺寸(英寸)': diagonalInches.toFixed(2)
    });
    
    console.log('\n🎯 11英寸屏幕检测结果:');
    console.log(`检测结果: ${detection.result ? '✅ 是11英寸屏幕' : '❌ 不是11英寸屏幕'}`);
    
    console.log('\n📊 详细检测信息:');
    console.table(detection.details);
    
    // 预期值对比
    console.log('\n📋 预期值对比:');
    
    const expectations = {
        '11英寸屏幕': {
            '物理像素': '1920×1200',
            '逻辑像素': '~1097×670',
            '像素密度比': '~1.75',
            '对角线尺寸': '~11英寸'
        },
        '15.6英寸屏幕': {
            '物理像素': '1920×1080',
            '逻辑像素': '1920×1080',
            '像素密度比': '1.0',
            '对角线尺寸': '~15.6英寸'
        }
    };
    
    console.table(expectations);
    
    // 建议
    console.log('\n💡 建议:');
    if (detection.result) {
        console.log('✅ 当前设备被正确识别为11英寸屏幕');
        console.log('✅ 系统将应用专门的显示优化');
        console.log('✅ 头部和表头高度将减半');
        console.log('✅ 字体和间距将适当缩小');
    } else {
        console.log('ℹ️ 当前设备未被识别为11英寸屏幕');
        if (deviceInfo.pixelRatio > 1.5) {
            console.log('⚠️ 检测到高像素密度比，可能需要调整检测参数');
        }
        if (diagonalInches >= 10 && diagonalInches <= 13) {
            console.log('⚠️ 对角线尺寸在11英寸范围内，可能需要调整检测逻辑');
        }
    }
    
    // 返回结果供进一步分析
    window.deviceDetectionResult = {
        deviceInfo,
        physicalPixels: {
            width: Math.round(physicalPixelWidth),
            height: Math.round(physicalPixelHeight)
        },
        physicalSize: {
            width: physicalWidth.toFixed(2),
            height: physicalHeight.toFixed(2),
            diagonal: diagonalInches.toFixed(2)
        },
        detection
    };
    
    console.log('\n🔧 调试信息已保存到 window.deviceDetectionResult');
    console.log('=== 检测完成 ===\n');
    
})();

// 使用说明
console.log(`
📖 使用说明:
1. 在目标设备的浏览器中打开开发者工具
2. 切换到 Console 标签页
3. 复制并粘贴此脚本代码
4. 按 Enter 键执行
5. 查看输出的检测结果和建议

🔍 如果检测结果不符合预期:
1. 检查物理像素是否接近 1920×1200
2. 检查像素密度比是否在 1.5-2.0 范围内
3. 检查对角线尺寸是否在 10.5-12.5 英寸范围内
4. 根据实际情况调整检测参数

📞 如需技术支持，请提供 window.deviceDetectionResult 的完整输出
`);
