# API日志记录功能使用说明

## 功能概述

本项目已集成API日志记录功能，可以自动记录所有API请求的详细信息，包括：
- 完整的请求地址
- 请求方法（GET/POST等）
- 请求头信息
- 请求参数（body）
- 响应数据

所有信息以完整的合法JSON格式打印，每个请求之间用分割线隔开。

## 开关配置功能

API日志记录功能支持灵活的开关配置，可以根据需要启用或禁用不同的功能模块：

### 配置选项

- **enabled**: 是否启用API日志记录（默认：true）
- **printToConsole**: 是否打印到控制台（默认：true）
- **saveToStorage**: 是否保存到本地存储（默认：false）
- **maxLogs**: 最大日志数量（默认：100）

### 配置方法

#### 1. 控制台调用

在浏览器控制台中直接调用全局函数：

```javascript
// 获取当前配置
getApiConfig();

// 设置配置
setApiConfig({
    enabled: true,
    printToConsole: true,
    saveToStorage: false,
    maxLogs: 100
});

// 启用/禁用API日志记录
enableApiLogging();
disableApiLogging();
toggleApiLogging();  // 切换状态

// 控制台打印开关
enableConsolePrint();
disableConsolePrint();

// 本地存储开关
enableStorageSave();
disableStorageSave();
```

#### 2. 代码调用

```javascript
import {
    getApiConfig,
    setApiConfig,
    enableApiLogging,
    disableApiLogging,
    toggleApiLogging,
    enableConsolePrint,
    disableConsolePrint,
    enableStorageSave,
    disableStorageSave
} from '@/utils/printApiInfo.js';

// 获取当前配置
const config = getApiConfig();
console.log('当前配置:', config);

// 设置配置
setApiConfig({
    enabled: true,
    printToConsole: true,
    saveToStorage: true,
    maxLogs: 200
});

// 启用API日志记录
enableApiLogging();
```

#### 3. 测试页面配置

访问 `pages/api-test/index.vue` 页面，可以通过UI界面进行配置：
- 查看当前配置状态
- 启用/禁用日志记录
- 启用/禁用控制台打印
- 启用/禁用本地存储
- 设置最大日志数量

### 配置持久化

当启用 `saveToStorage` 时，配置会自动保存到本地存储，下次应用启动时会自动恢复配置。

## 使用方法

### 1. 自动记录

所有API请求都会根据当前配置自动记录。默认情况下，API日志记录功能是启用的，当应用发起任何API请求时，系统会自动在控制台打印请求的详细信息。

### 2. 配置优先级

1. **本地存储配置**：如果启用了 `saveToStorage`，会优先使用本地存储中的配置
2. **代码配置**：通过代码设置的配置会覆盖本地存储配置
3. **默认配置**：如果没有其他配置，使用默认配置

### 2. 手动查看日志

#### 在控制台中使用全局函数

在H5环境中，以下函数已挂载到全局window对象，可直接在浏览器控制台中调用：

```javascript
// 打印所有API请求信息
printAllApiInfo();

// 导出所有API日志为JSON字符串
exportAllApiLogs();

// 清空所有API日志
clearAllApiLogs();

// 打印API统计信息
printApiStats();

// 配置相关函数
getApiConfig();                    // 获取配置
setApiConfig(config);              // 设置配置
enableApiLogging();                // 启用日志记录
disableApiLogging();               // 禁用日志记录
toggleApiLogging();                // 切换日志记录状态
enableConsolePrint();              // 启用控制台打印
disableConsolePrint();             // 禁用控制台打印
enableStorageSave();              // 启用本地存储
disableStorageSave();             // 禁用本地存储
```

#### 在代码中使用

```javascript
import {
    printAllApiInfo,
    clearAllApiLogs,
    printApiStats,
    getApiConfig,
    setApiConfig,
    enableApiLogging,
    disableApiLogging
} from '@/utils/printApiInfo.js';

// 打印所有API请求信息
printAllApiInfo();

// 清空API日志
clearAllApiLogs();

// 打印API统计信息
printApiStats();

// 获取当前配置
const config = getApiConfig();

// 启用API日志记录
enableApiLogging();

// 禁用API日志记录
disableApiLogging();

// 自定义配置
setApiConfig({
    enabled: true,
    printToConsole: true,
    saveToStorage: false,
    maxLogs: 50
});
```

### 3. 访问测试页面

访问 `pages/api-test/index.vue` 页面，可以：
- 查看API日志记录功能的使用说明
- 测试各种API请求
- 手动触发打印API日志
- 查看API统计信息
- **配置API日志记录功能**：
  - 查看当前配置状态
  - 启用/禁用日志记录
  - 启用/禁用控制台打印
  - 启用/禁用本地存储
  - 设置最大日志数量

## 输出格式示例

```
========================================
API请求 #1
========================================
请求信息:
{
  "url": "http://124.222.67.60:8091/conceptual/ppvt/user/1.0/login",
  "method": "POST",
  "headers": {
    "content-type": "application/json",
    "sourceType": "SMSCODE",
    "token": "your_token_here"
  },
  "body": {
    "username": "test",
    "password": "123456"
  }
}

响应信息:
{
  "code": "0000",
  "message": "success",
  "data": {
    "userId": "12345",
    "token": "your_token_here"
  }
}
========================================
```

## 文件结构

```
utils/
├── apiLogger.js          # API日志记录核心功能
├── interceptors.js       # HTTP请求拦截器（已集成日志记录）
├── request.js            # HTTP请求封装（已支持日志记录）
└── printApiInfo.js       # API日志打印工具

pages/
└── api-test/
    └── index.vue         # API测试页面
```

## 实现原理

1. **请求拦截器**：在发送请求前，记录请求的URL、方法、头部和数据。
2. **响应拦截器**：在收到响应后，记录响应数据，并将请求和响应信息一起打印。
3. **日志格式化**：使用JSON.stringify格式化输出，确保输出的是合法的JSON格式。
4. **分割线**：每个请求之间使用分割线分隔，提高可读性。
5. **配置管理**：
   - 支持动态配置，可随时启用/禁用功能
   - 配置持久化，可保存到本地存储
   - 配置优先级管理，确保配置的正确应用

## 注意事项

1. **生产环境安全**：日志记录功能仅在开发环境中启用，生产环境建议关闭以避免敏感信息泄露。
2. **性能影响**：日志记录会增加少量性能开销，建议在需要调试时启用。
3. **敏感信息**：包含token等敏感信息，在生产环境中使用时请注意安全。
4. **存储限制**：本地存储有大小限制，建议合理设置 `maxLogs` 参数。
5. **配置持久化**：启用 `saveToStorage` 后，配置会保存到本地，注意在不需要时及时清理。

## 自定义配置

### 1. 修改默认配置

如需修改默认配置，可以编辑 `utils/apiLogger.js` 文件中的 `apiLogConfig` 对象：

```javascript
let apiLogConfig = {
    enabled: false,          // 默认禁用
    printToConsole: true,    // 启用控制台打印
    saveToStorage: false,    // 禁用本地存储
    maxLogs: 50              // 最大日志数量
};
```

### 2. 自定义输出格式

如需自定义日志记录行为，可以修改 `utils/apiLogger.js` 文件中的相关函数：

```javascript
// 修改分割线样式
const printDivider = () => {
    console.log('==================== 你的自定义分割线 ====================');
};

// 修改JSON格式化方式
const formatJSON = (obj) => {
    // 自定义格式化逻辑
    return JSON.stringify(obj, null, 2);
};

// 自定义日志记录逻辑
const logApiRequest = (url, method, headers, data, response) => {
    // 自定义记录逻辑
    // 例如：只记录特定URL的请求
    if (url.includes('api')) {
        // 记录日志
    }
};
```

### 3. 环境配置

可以根据不同环境设置不同的默认配置：

```javascript
// 在 initApiLogConfig 函数中添加环境判断
const initApiLogConfig = () => {
    // 基础配置
    let baseConfig = {
        enabled: true,
        printToConsole: true,
        saveToStorage: false,
        maxLogs: 100
    };
    
    // 根据环境调整配置
    if (process.env.NODE_ENV === 'production') {
        baseConfig.enabled = false;
        baseConfig.printToConsole = false;
    }
    
    // 尝试从本地存储读取配置
    try {
        const savedConfig = uni.getStorageSync('apiLogConfig');
        if (savedConfig) {
            apiLogConfig = { ...baseConfig, ...savedConfig };
        } else {
            apiLogConfig = baseConfig;
        }
    } catch (e) {
        console.error('读取API日志配置失败:', e);
        apiLogConfig = baseConfig;
    }
};