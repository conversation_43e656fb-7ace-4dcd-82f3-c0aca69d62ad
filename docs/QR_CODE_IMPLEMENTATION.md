# 二维码功能实现说明

## 功能概述
在测评详情页面 `pages/assessment/detail.vue` 中实现了家长部分和教师部分的二维码生成和获取逻辑。

## 实现的功能

### 1. API 接口添加 (service/workorder.js)
- `generalWorkOrderQrCode`: 生成工单二维码
- `qryWorkOrderQrCodeInfo`: 查询工单二维码信息

### 2. 数据字段添加
- `qrCodeBase64`: 二维码图片base64数据
- `qrCodeExpireTime`: 二维码过期时间
- `currentQRCodeId`: 当前二维码ID

### 3. 核心方法实现

#### showQRCode() - 显示二维码
- 检查当前组是否已有二维码ID
- 如果有ID，直接获取二维码信息
- 如果没有ID，先生成二维码再获取信息

#### generateQRCode(respondentGroup) - 生成二维码
- 调用 `generalWorkOrderQrCode` 接口生成二维码
- 重新加载工单详情获取新的qrCodeId
- 自动获取生成的二维码信息

#### fetchQRCodeInfo(qrCodeId) - 获取二维码信息
- 调用 `qryWorkOrderQrCodeInfo` 接口获取二维码详情
- 设置二维码图片、过期时间和状态
- 根据 qrCodeState 判断是否失效

#### regenerateQRCode() - 重新生成二维码
- 调用生成二维码方法
- 显示加载状态和成功提示

### 4. UI 更新
- 二维码图片支持显示真实的base64数据
- 过期时间显示真实的接口返回时间
- 失效状态根据接口返回的 qrCodeState 判断

## 业务流程

### 首次显示二维码
1. 用户点击"家长二维码"或"教师二维码"按钮
2. 检查当前组的 qrCodeId 是否为 null
3. 如果为 null，调用生成接口
4. 生成成功后，重新获取工单详情
5. 使用新的 qrCodeId 获取二维码信息
6. 显示二维码弹窗

### 已有二维码时显示
1. 用户点击二维码按钮
2. 直接使用现有的 qrCodeId 获取二维码信息
3. 显示二维码弹窗

### 重新生成二维码
1. 用户在弹窗中点击"重新生成"按钮
2. 调用生成接口重新生成二维码
3. 更新显示的二维码信息

## 接口参数说明

### 生成二维码接口
```javascript
{
    "workOrderId": "940680688171094016",
    "respondentGroup": "P" // P: 家长, T: 教师
}
```

### 查询二维码接口
```javascript
{
    "qrCodeId": "941475277366759424"
}
```

## 状态管理
- `qrCodeState`: "1" 表示有效，"0" 表示失效
- 失效时显示红色提示文字和重新生成按钮
- 有效时显示蓝色过期时间文字

## 页面优化

### 1. 标签页状态保持
- 修改 `loadWorkOrderDetail()` 方法，添加 `keepCurrentTab` 参数
- 在生成二维码后刷新数据时，保持当前选中的标签页状态
- 避免用户在家长部分或教师部分生成二维码后跳转到孩子部分

### 2. 二维码弹窗文字居中
- 修改 `.qr-main-text` 样式中的 `text-align` 属性
- 从 `justify`（两端对齐）改为 `center`（居中对齐）
- 确保"家长微信扫一扫，手机端完成测评项目"等文字在弹窗中居中显示

## 技术实现细节

### 标签页状态保持逻辑
```javascript
async loadWorkOrderDetail(keepCurrentTab = false) {
    // 保存当前选中的标签页索引
    const currentTabIndex = this.activeTabIndex;

    // ... 数据加载逻辑 ...

    // 设置标签页选中状态
    if (this.respondentGroups.length > 0) {
        if (keepCurrentTab && currentTabIndex < this.respondentGroups.length) {
            // 保持当前选中的标签页
            this.activeTabIndex = currentTabIndex;
        } else {
            // 设置默认选中第一个标签页
            this.activeTabIndex = 0;
        }
    }
}
```

### 生成二维码时保持状态
```javascript
// 生成成功，重新加载工单详情获取新的qrCodeId，保持当前选中的标签页
await this.loadWorkOrderDetail(true);
```
