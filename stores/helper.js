import {
	defineStore
} from 'pinia';
import {
	getLine
} from '@/utils/bandpass_filter_offline.js'
import {
	DbayLineNum,
	CTLineNum,
	DbayLineNumFull,
	CTLineNumFull
} from '../common/global';

export const useHelper = defineStore('helper', {
	state: () => {
		return {
			helperBlu: null, //深湾设备
			address: '', //正在连接的设备编号
			bluData: null, //设备传递的数据
			numBox: { //原始值
				AF7Data: [],
				AF8Data: [],
			},
			isPushBox: false,
			isFirst: false, //完成第一次数据采集
			amplitudeDateArr: { //算法算好后线点的集合
				AF7Data: [],
				AF8Data: []
			},
			isInit: false, //是否初始化深湾蓝牙设备
			bluetoothAdapter: null,
			connectingDevice: {
				name: ''
			}, //正在连接的设备
			electricity: '电量获取中...'
		};
	},
	actions: {
		getAddress(value) {
			this.address = value
		},
		getBlu(value) {
			this.bluData = value
		},
		getIsFirst(value) {
			this.isFirst = value
		},
		changePushBox(value) {
			this.isPushBox = value
		},
		addAmplitudeDate(value) {
			if (this.address.substring(0, 4) === 'Dbay') {
				if (this.numBox.AF7Data.length < DbayLineNum) {
					this.numBox.AF7Data = [...this.numBox.AF7Data, ...value.AF7Data]
					this.numBox.AF8Data = [...this.numBox.AF8Data, ...value.AF8Data]
				}
			} else {
				if (this.numBox.AF7Data.length <= CTLineNum) {
					this.numBox.AF7Data = [...this.numBox.AF7Data, ...value.AF7Data]
					this.numBox.AF8Data = [...this.numBox.AF8Data, ...value.AF8Data]
				}
			}

		},
		addAmplitudeDateFull(value) {
			if (this.address.substring(0, 4) === 'Dbay') {
				if (this.numBox.AF7Data.length < DbayLineNumFull) {
					this.numBox.AF7Data = [...this.numBox.AF7Data, ...value.AF7Data]
					this.numBox.AF8Data = [...this.numBox.AF8Data, ...value.AF8Data]
				}
			} else {
				if (this.numBox.AF7Data.length <= CTLineNumFull) {
					this.numBox.AF7Data = [...this.numBox.AF7Data, ...value.AF7Data]
					this.numBox.AF8Data = [...this.numBox.AF8Data, ...value.AF8Data]
				}
			}

		},
		resetBox() {
			this.numBox.AF7Data = []
			this.numBox.AF8Data = []
		},
		getAmplitudeArr() {
			if (this.address.substring(0, 4) === 'Dbay') {
				if (this.numBox.AF7Data.length >= DbayLineNum) {
					this.isFirst = true
					const arr = this.numBox.AF7Data
					const arr1 = this.numBox.AF8Data
					this.amplitudeDateArr.AF7Data = getLine(arr, 250)
					this.amplitudeDateArr.AF8Data = getLine(arr1, 250)
					this.resetBox()
					return true
				} else {
					return false
				}
			} else {
				if (this.numBox.AF7Data.length === CTLineNum) {
					this.isFirst = true
					const arr = this.numBox.AF7Data
					const arr1 = this.numBox.AF8Data
					this.amplitudeDateArr.AF7Data = getLine(arr, 500)
					this.amplitudeDateArr.AF8Data = getLine(arr1, 500)
					this.resetBox()
					return true
				} else {
					return false
				}
			}

		},
		getAmplitudeArrFull() {
			if (this.address.substring(0, 4) === 'Dbay') {
				if (this.numBox.AF7Data.length >= DbayLineNumFull) {
					this.isFirst = true
					const arr = this.numBox.AF7Data
					const arr1 = this.numBox.AF8Data
					this.amplitudeDateArr.AF7Data = getLine(arr, 250)
					this.amplitudeDateArr.AF8Data = getLine(arr1, 250)
					this.resetBox()
					return true
				} else {
					return false
				}
			} else {
				if (this.numBox.AF7Data.length === CTLineNumFull) {
					this.isFirst = true
					const arr = this.numBox.AF7Data
					const arr1 = this.numBox.AF8Data
					this.amplitudeDateArr.AF7Data = getLine(arr, 500)
					this.amplitudeDateArr.AF8Data = getLine(arr1, 500)
					this.resetBox()
					return true
				} else {
					return false
				}
			}

		},
	}
});