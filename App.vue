<!--
 * @Description: 
 * @Author: 小雨
 * @Date: 2023-08-08 09:23:44
 * @LastEditTime: 2024-07-04 14:02:12
 * @LastEditors: 小雨
-->
<script>
	// 引入API日志记录工具
	import {
		printAllApiInfo,
		exportAllApiLogs,
		clearAllApiLogs,
		printApiStats,
		getApiConfig,
		setApiConfig,
		enableApiLogging,
		disableApiLogging,
		toggleApiLogging,
		enableConsolePrint,
		disableConsolePrint,
		enableStorageSave,
		disableStorageSave
	} from './utils/printApiInfo.js';
	
	// 引入响应式适配工具
	import { initResponsive } from './utils/responsive.js';
	
	// 引入屏幕检测工具
	import { initScreenDetection } from './utils/screenDetector.js';
	
	export default {
		onLaunch: async function () {
			console.log('App Launch')
			
			// 初始化屏幕检测
			try {
				await initScreenDetection();
				console.log('屏幕分辨率检测完成');
			} catch (error) {
				console.error('屏幕分辨率检测失败:', error);
			}
			
			// 初始化响应式适配
			try {
				await initResponsive();
				console.log('全局响应式适配初始化完成');
			} catch (error) {
				console.error('全局响应式适配初始化失败:', error);
			}
			
			// 将API日志工具函数挂载到全局
			// #ifdef H5
			if (typeof window !== 'undefined') {
				window.printAllApiInfo = printAllApiInfo;
				window.exportAllApiLogs = exportAllApiLogs;
				window.clearAllApiLogs = clearAllApiLogs;
				window.printApiStats = printApiStats;
				window.getApiConfig = getApiConfig;
				window.setApiConfig = setApiConfig;
				window.enableApiLogging = enableApiLogging;
				window.disableApiLogging = disableApiLogging;
				window.toggleApiLogging = toggleApiLogging;
				window.enableConsolePrint = enableConsolePrint;
				window.disableConsolePrint = disableConsolePrint;
				window.enableStorageSave = enableStorageSave;
				window.disableStorageSave = disableStorageSave;
				
				console.log('API日志工具已挂载到全局，可在控制台使用以下命令：');
				console.log('  printAllApiInfo() - 打印所有API请求信息');
				console.log('  exportAllApiLogs() - 导出所有API日志');
				console.log('  clearAllApiLogs() - 清空所有API日志');
				console.log('  printApiStats() - 打印API统计信息');
				console.log('  getApiConfig() - 获取API日志配置');
				console.log('  setApiConfig(config) - 设置API日志配置');
				console.log('  enableApiLogging() - 启用API日志记录');
				console.log('  disableApiLogging() - 禁用API日志记录');
				console.log('  toggleApiLogging() - 切换API日志记录状态');
				console.log('  enableConsolePrint() - 启用控制台打印');
				console.log('  disableConsolePrint() - 禁用控制台打印');
				console.log('  enableStorageSave() - 启用本地存储');
				console.log('  disableStorageSave() - 禁用本地存储');
			}
			// #endif
			
			// #ifdef APP-PLUS
			plus.navigator.setFullscreen(true);
			plus.screen.lockOrientation('landscape-primary');
			uni.reLaunch({
				url: "/pages/home/<USER>",
			})
			// #endif
		},
		onShow: function () {
			console.log('App Show')
		},
		onHide: function () {
			console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	/*每个页面公共css */
	@import './styles/responsive.scss';
	/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
	@font-face {
		font-family: 'iconfont';
		/* Project id 4518473 */
		src: url('https://at.alicdn.com/t/c/font_4518473_w1gltnctvj.woff2?t=1718330683366') format('woff2'),
			url('https://at.alicdn.com/t/c/font_4518473_w1gltnctvj.woff?t=1718330683366') format('woff'),
			url('https://at.alicdn.com/t/c/font_4518473_w1gltnctvj.ttf?t=1718330683366') format('truetype');
	}

	view,
	text,
	scroll-view,
	input,
	button,
	image,
	cover-view {
		box-sizing: border-box;
	}

	image {
		width: 100%;
		height: auto;
	}

	page {
		position: relative;
	}

	.iconfont {
		font-family: "iconfont";
	}

	.center {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.bgImg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		z-index: -1;
	}


	:deep(.uni-forms-item__label) {
		font-size: 36rpx !important;
	}
</style>