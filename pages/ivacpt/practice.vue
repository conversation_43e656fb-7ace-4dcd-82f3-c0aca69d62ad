<template>
	<clickEffect :isClick="state.isClick"></clickEffect>
	<IvaLineVue></IvaLineVue>
	<view class="iva" @click="click" @longpress="langTime" @touchend="state.showlangTime=false">
		<StepsFour :options="state.stepList" :active="state.active>=3?'3':'2'"></StepsFour>
		<view class="iva-content">
			<view class="iva-content-box" v-if="state.active===1">
				<view class="iva-content-box-title">
					<view class="iva-content-box-title-icon center iconfont">&#xe6b3;</view>
					<text class="iva-content-box-title-text"> {{rules[state.teach].text}}</text>
				</view>
				<view class="iva-content-box-value center">
					<view class="iva-content-box-value-other center">
						<view class="iva-content-box-value-other-other1 center">
							{{state.showNum1?'1':state.showNum2?'2':''}}
						</view>
					</view>
				</view>
				<text class="iva-content-box-tips">
					{{rules[state.teach].rule}}
				</text>
				<view class="iva-content-box-btn center" v-if="state.teach===3" @click.stop="()=>train()">
					开始练习操作
				</view>
			</view>
			<view v-if="state.active===2" class="iva-content-box">
				<view class="iva-content-box-title">
				</view>
				<view class="iva-content-box-value center">
					<view class="iva-content-box-value-other center">
						<view class="iva-content-box-value-other-other1 center">
							{{state.showNum1?'1':state.showNum2?'2':''}}
						</view>
					</view>
				</view>
				<view class="iva-content-box-oops" v-show="state.showOpps">
					oops
				</view>
				<view class="iva-content-box-langtips" v-show="state.showlangTime">
					按住屏幕时间请不要太长
				</view>
			</view>
			<view v-if="state.active===3" class="iva-content-box">
				<view class="iva-content-box-title">
					<view class="iva-content-box-title-icon center iconfont">&#xe6b3;</view>
					<text class="iva-content-box-title-text"> {{rules[state.teach].text}}</text>
				</view>
				<view class="iva-content-box-value center">
					<view class="iva-content-box-value-other center">
						<view class="iva-content-box-value-other-other1 center">
							{{state.showNum1?'1':state.showNum2?'2':''}}
						</view>
					</view>
				</view>
				<text class="iva-content-box-tips">

				</text>
				<view class="iva-content-box-btn center" v-if="state.teach===4" @click.stop="()=>mainTest()">
					开始主测试
				</view>
			</view>
			<view v-if="state.active===4" class="iva-content-box">
				<view class="iva-content-box-title">
				</view>
				<view class="iva-content-box-value center">
					<view class="iva-content-box-value-other center">
						<view class="iva-content-box-value-other-other1 center">
							{{state.showNum1?'1':state.showNum2?'2':''}}
						</view>
					</view>
				</view>
			</view>
			<view v-if="state.active===5" class="iva-content-box">
				<view class="iva-content-box-title">
				</view>
				<view class="iva-content-box-value center">
					<view class="iva-content-box-value-other center">
						<view class="iva-content-box-value-other-other1 center">
							{{state.showNum1?'1':state.showNum2?'2':''}}
						</view>
					</view>
				</view>
				<text class="iva-content-box-tips">
					{{rules[state.active].rule}}
				</text>
				<view class="iva-content-box-btn center" v-if="state.active===5" @click.stop="go">
					开始恢复性测试
				</view>
			</view>
		</view>
	</view>
	<IvaDropoutReminderVue :eceuId="eceuId" :isStartCollect="isStartCollect"></IvaDropoutReminderVue>
</template>

<script setup>
	import IvaLineVue from '@/components/IvaLine.vue';
	import StepsFour from '@/components/StepsFour.vue';
	import IvaDropoutReminderVue from '../../components/IvaDropoutReminder.vue';
	import {
		onMounted,
		onUnmounted,
		reactive,
		ref,
		watch
	} from "vue";
	import {
		getStorageSync,
		navigateTo,
		redirectTo
	} from '@/common/uniTool';
	import PopBoxVue from '@/components/PopBox.vue';
	import {
		getIvacptData,
		subIvacptStdData
	} from '@/service/ivacpt';
	import {
		useUserStore
	} from '../../stores/user';
	import clickEffect from '@/components/pc-clickEffect/pc-clickEffect.vue'
	import getIVAData from './ivaData';
	const userStore = useUserStore()
	const innerAudioContextRef = ref(null) //音频
	const backAudioContextRef = ref(null) //音频
	const clickAudioContextRef = ref(null) //音频
	const timeRef = ref(null) //定时器
	const eventInfos = ref([]) //事件信息
	const eceuId = ref('') //时间戳id
	const isStartCollect = ref(false) //是否开始脑电采集
	const props = defineProps(['evaluatId'])
	const rules = [{
		text: '现在你将看到或听到数字“1”或者“2”。这些数字将以不同的方式被混合在一起,像这样。',
		rule: '',
	}, {
		text: '现在你将看到或听到数字“1”或者“2”。这些数字将以不同的方式被混合在一起,像这样。',
		rule: '当你听到或者看到数字“1”，你就单击屏幕。尽可能快一些，但也要小心。',
	}, {
		text: '如果你漏掉了1或者错点了2，计算机会说:“oops”。但在主测试阶段不会提示。',
		rule: '',
	}, {
		text: '如果你漏掉了1或者错点了2，计算机会说:“oops”。但在主测试阶段不会提示。',
		rule: '当你准备好开始之后，点击“开始练习操作”。',
	}, {
		text: '这个测试将持续大约15分钟，尽最大努力保持警惕。如果你点错了，没关系，继续测试。',
		rule: '',
	}, {
		text: '主测试已完成',
		rule: '',
	}]
	const state = reactive({
		stepList: [{
			title: '热身阶段'
		}, {
			title: '练习阶段'
		}, {
			title: '主测试阶段'
		}, {
			title: '恢复阶段'
		}],
		active: 1,
		step: 0, //音频步骤
		isMp3End: false,
		teach: 0, //音频步骤
		showNum1: false,
		showNum2: false,
		testRound: 0, //测试轮次
		isClick: false, //是否开始添加数据
		clickErr: 0, //连续错误次数
		ivaData: null,
		evaluatId: '',
		showOpps: false,
		showlangTime: false
	})

	onMounted(() => {
		getData('2')
		innerAudioContextRef.value = uni.createInnerAudioContext()
		backAudioContextRef.value = uni.createInnerAudioContext()

		backAudioContextRef.value.src = '/static/audio/ivacpt/pra-1.mp3'
		backAudioContextRef.value.play()
		backAudioContextRef.value.onEnded(() => {
			if (state.step === 1) {
				backAudioContextRef.value.destroy()
				backAudioContextRef.value = null
				state.isMp3End = true
			}
			if (state.step === 0) {
				playNum('1')
				setTimeout(() => {
					showNum('2')
					setTimeout(() => {
						playNum('2')
						setTimeout(() => {
							showNum('1')
							setTimeout(() => {
								state.teach = 1
								backAudioContextRef.value.src = '/static/audio/ivacpt/pra-2.mp3'
								backAudioContextRef.value.play()
								state.step = 1
							}, 2760)
						}, 2760)
					}, 2760)
				}, 2760)
			}
		})

	})
	const getData = (num) => {
		getIvacptData({
			outpatientId: userStore.outpatientId,
			type: num,
			evaluatId: num == 2 ? props.evaluatId : state.ivaData.evaluatId,
			version: 'STD'
		}).then(res => {
			console.log(res);
			state.ivaData = res.data
			eceuId.value = res.data.eceuId
		})
	}
	onUnmounted(() => {
		innerAudioContextRef.value && innerAudioContextRef.value.destroy()
		backAudioContextRef.value && backAudioContextRef.value.destroy()
		clickAudioContextRef.value && clickAudioContextRef.value.destroy()
		timeRef.value && clearInterval(timeRef.value)
	})

	watch(() => state.active, (active) => {
		if (active === 3) { //练习阶段结束进入主测试阶段介绍阶段可以在此提交数据
			getData('1')
			state.testRound = 0
			state.teach = 4 //主测试介绍页面
			backAudioContextRef.value = uni.createInnerAudioContext()
			backAudioContextRef.value.src = '/static/audio/ivacpt/pra-5.mp3'
			backAudioContextRef.value.play()
		}
	})
	watch([() => state.step, () => state.isMp3End], ([step, isMp3End]) => {
		if (step === 1 && isMp3End) {
			state.teach = 2
			backAudioContextRef.value = uni.createInnerAudioContext()
			backAudioContextRef.value.src = '/static/audio/ivacpt/pra-3.mp3'
			backAudioContextRef.value.play()
			backAudioContextRef.value.onEnded(() => {
				if (state.step === 1) {
					backAudioContextRef.value.src = '/static/audio/ivacpt/pra-4.mp3'
					backAudioContextRef.value.play()
					state.teach = 3
					state.step = 2
				}
			})
		} else if (state.step === 2) {
			backAudioContextRef.value.onEnded(() => {

			})
		} else if (state.step === 3) {
			backAudioContextRef.value.onEnded(() => {

			})
		}
	})
	const langTime = () => {
		if (state.active === 2) {
			state.showlangTime = true
		}
	}
	const getType = (round, level) => {
		const data = getIVAData('', level)
		let num = data['numList'][round]
		let type = data['signalList'][round]
		let modType = data['modTypelList'][round]
		let signal = data['signalList'][round]
		if (type === 'A') {
			playNum(num, modType, signal)
		}
		if (type === "V") {
			showNum(num, modType, signal)
		}
	}

	const showNum = (num, modType, signal) => {
		if (state.isClick) {
			eventInfos.value.push({
				beginDate: new Date().getTime(),
				clickDate: [],
				modType,
				num,
				signal
			})
		}
		if (num === '1') {
			state.showNum1 = true
			setTimeout(() => {
				state.showNum1 = false
			}, 200)
		} else {
			state.showNum2 = true
			setTimeout(() => {
				state.showNum2 = false
			}, 200)
		}

	}
	const playNum = (num, modType, signal) => {
		if (state.isClick) {
			eventInfos.value.push({
				beginDate: new Date().getTime(),
				clickDate: [],
				modType,
				num,
				signal
			})
		}
		if (num === '1') {
			innerAudioContextRef.value.src = '/static/audio/ivacpt/iva-1.mp3'
			innerAudioContextRef.value.play()
		} else {
			innerAudioContextRef.value.src = '/static/audio/ivacpt/iva-2.mp3'
			innerAudioContextRef.value.play()
		}
	}

	const postData = (num) => {
		isStartCollect.value = false
		subIvacptStdData({
			evaluatId: state.ivaData.evaluatId,
			outpatientId: userStore.outpatientId,
			type: num,
			eventInfos: eventInfos.value
		}).then(res => {
			console.log(res);
			eventInfos.value = [] //提交数据后清空数据
		})
	}
	const click = (e) => {
		if (state.isClick) {
			eventInfos.value[state.testRound - 1]['clickDate'].push(new Date().getTime())
			if (state.active === 2 && state.ivaData.numList[state.testRound - 1] == 2) {
				clickAudioContextRef.value = uni.createInnerAudioContext()
				clickAudioContextRef.value.src = '/static/audio/ivacpt/oops.mp3'
				clickAudioContextRef.value.play()
				state.showOpps = true
			}
		}
	}
	const mainTest = () => {
		isStartCollect.value = true
		backAudioContextRef.value && backAudioContextRef.value.destroy()
		state.active = 4
		timeRef.value = setInterval(() => {
			state.testRound++ //轮次加一
			state.isClick = true
			getType(state.testRound - 1, '1')
			if (state.testRound === state.ivaData.numList.length) {
				clearInterval(timeRef.value)
				setTimeout(() => {
					state.isClick = false
					state.step = 3
					backAudioContextRef.value = uni.createInnerAudioContext()
					backAudioContextRef.value.src = '/static/audio/ivacpt/pra-6.mp3'
					backAudioContextRef.value.play()
					state.active = 5
					postData('1')
					console.log('主测试阶段结束');
				}, 1500)
			}
		}, 1500)
	}
	const train = () => {
		backAudioContextRef.value && backAudioContextRef.value.destroy()
		isStartCollect.value = true
		state.active = 2
		state.isClick = false
		setTimeout(() => {
			practiceStart()
		}, 2700)
	}
	const practiceStart = () => {
		backAudioContextRef.value && backAudioContextRef.value.destroy()
		state.testRound++
		state.showOpps = false
		state.isClick = true
		getType(state.testRound - 1, '2')
		if (state.testRound === state.ivaData.numList.length) { //练习环节结束进入主测试环节
			clearInterval(timeRef.value)
			setTimeout(() => {
				state.isClick = false
				postData('2')
				console.log('结束热身');
				setTimeout(() => {
					state.active = 3
				}, 550)
			}, 2760)
		} else {
			timeRef.value = setInterval(() => {
				if (eventInfos.value[state.testRound - 1]['clickDate'].length === 0 && state.ivaData.numList[state.testRound - 1] == 1) {
					clearInterval(timeRef.value)
					//播放声音
					backAudioContextRef.value = uni.createInnerAudioContext()
					backAudioContextRef.value.src = '/static/audio/ivacpt/oops.mp3'
					backAudioContextRef.value.play()
					state.showOpps = true
					backAudioContextRef.value.onEnded(() => {
						state.showOpps = false
						backAudioContextRef.value.destroy()
						backAudioContextRef.value = null
						setTimeout(() => {
							practiceStart(2)
						}, 320)
					})
				} else {
					state.testRound++ //轮次加一
					state.showOpps = false
					getType(state.testRound - 1, '2')
					if (state.testRound === state.ivaData.numList.length) { //练习环节结束进入主测试环节
						clearInterval(timeRef.value)
						setTimeout(() => {
							state.isClick = false
							postData('2')
							console.log('结束热身 ');
							setTimeout(() => {
								state.active = 3
							}, 550)
						}, 2760)
					}
				}
			}, 2760)
		}
	}

	const go = () => {
		redirectTo(`/pages/ivacpt/standard?type=1&evaluatId=${state.ivaData.evaluatId}`)
	}
</script>

<style lang="scss">
	.iva {
		width: 100vw;
		height: 100vh;
		padding-top: 60rpx;
		position: fixed;
		z-index: 1;

		&-step {
			width: 120%;
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
		}

		&-content {
			height: 84vh;
			width: 100%;
			position: absolute;
			top: 12%;
			padding: 26rpx;

			&-box {
				display: flex;
				flex-direction: column;
				align-items: center;

				&-oops {
					font-size: 52rpx;
					color: #F2B103;
				}

				&-langtips {
					color: #FFDF9E;
					font-size: 40rpx;
					position: absolute;
					bottom: 100rpx;
					left: 50%;
					transform: translateX(-50%);
					width: 100%;
					text-align: center;
				}

				&-btn {
					width: 480rpx;
					height: 116rpx;
					background: #FFFFFF;
					border-radius: 108rpx;
					font-size: 48rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: bold;
					color: #111111;
				}

				&-tril {
					font-size: 40rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: bold;
					color: #FFAB01;
				}

				&-rule {
					display: flex;
					flex-direction: column;
					align-items: center;

					&-img {
						width: 369rpx;
						margin-top: 20rpx;
					}

					&-text {
						font-size: 40rpx;
						font-family: SourceHanSansCN-Bold, SourceHanSansCN;
						font-weight: bold;
						color: #ffffff;
						margin-top: 40rpx;
						margin-bottom: 140rpx;
					}

					&-btn {
						width: 628rpx;
						height: 120rpx;
						background: #287FFF;
						border-radius: 74rpx;
						font-size: 40rpx;
						font-family: SourceHanSansCN-Bold, SourceHanSansCN;
						font-weight: bold;
						color: #FFFFFF;
					}
				}

				&-tips {
					font-family: SourceHanSansCN, SourceHanSansCN;
					font-weight: bold;
					font-size: 53rpx;
					color: #F2B103;
					text-align: center;
					margin-bottom: 20rpx;
				}

				&-value {
					width: 619rpx;
					height: 748rpx;
					border-radius: 30rpx;
					border: 6rpx solid #F2B103;
					margin-top: 26rpx;
					margin-bottom: 20rpx;

					&-other {
						width: 581rpx;
						height: 710rpx;
						border-radius: 30rpx;
						border: 6rpx solid #F2B103;

						&-other1 {
							width: 544rpx;
							height: 673rpx;
							border-radius: 30rpx;
							border: 6rpx solid #F2B103;
							font-family: SourceHanSansCN, SourceHanSansCN;
							font-weight: bold;
							font-size: 600rpx;
							color: #00B210;
						}
					}
				}

				&-title {
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: bold;
					font-size: 52rpx;
					color: #FFFFFF;
					display: flex;
					align-items: flex-start;
					width: 92%;
					height: 100rpx;

					&-text {
						flex: 1;
					}

					&-icon {
						font-weight: normal;
						border-radius: 50%;
						width: 60rpx;
						height: 60rpx;
						background: #FFFFFF;
						color: #287FFF;
						margin-right: 10rpx;
						flex-shrink: 0;
					}
				}
			}
		}
	}
</style>