<template>
    <view class="responsive-test-page responsive-container">
        <!-- 状态栏 -->
        <view class="status-bar">
            <view class="time">{{ currentTime }}</view>
            <view class="status-icons">
                <!-- 状态栏图标区域 -->
            </view>
        </view>
        
        <!-- 顶部功能区 -->
        <view class="header">
            <view class="header-bg"></view>
            <view class="back-btn" @click="goBack">
                <view class="back-arrow"></view>
            </view>
            <text class="title">响应式测试页面</text>
        </view>
        
        <!-- 设备信息显示 -->
        <view class="device-info">
            <view class="info-card">
                <text class="card-title">设备信息</text>
                <view class="info-item">
                    <text class="label">屏幕分辨率:</text>
                    <text class="value">{{ deviceInfo.windowWidth }} × {{ deviceInfo.windowHeight }}</text>
                </view>
                <view class="info-item">
                    <text class="label">像素比:</text>
                    <text class="value">{{ deviceInfo.pixelRatio }}</text>
                </view>
                <view class="info-item">
                    <text class="label">物理尺寸:</text>
                    <text class="value">{{ physicalSize.width }}" × {{ physicalSize.height }}"</text>
                </view>
                <view class="info-item">
                    <text class="label">对角线尺寸:</text>
                    <text class="value">{{ physicalSize.diagonal }}"</text>
                </view>
                <view class="info-item">
                    <text class="label">是否11英寸屏幕:</text>
                    <text class="value" :class="{ 'highlight': scaleInfo.is11InchScreen }">
                        {{ scaleInfo.is11InchScreen ? '是' : '否' }}
                    </text>
                </view>
                <view class="info-item">
                    <text class="label">缩放比例:</text>
                    <text class="value">{{ scaleInfo.finalScale }}</text>
                </view>
                <view class="info-item">
                    <text class="label">头部高度(通用):</text>
                    <text class="value">{{ headerHeight }}px</text>
                </view>
                <view class="info-item">
                    <text class="label">头部高度(测评记录):</text>
                    <text class="value">{{ headerHeightRecord }}px</text>
                </view>
                <view class="info-item">
                    <text class="label">表头高度:</text>
                    <text class="value">{{ tableHeaderHeight }}px</text>
                </view>
            </view>
        </view>
        
        <!-- 样式测试区域 -->
        <view class="test-area">
            <view class="test-card">
                <text class="card-title">字体大小测试</text>
                <view class="font-samples">
                    <text class="font-xs">超小字体 (12px)</text>
                    <text class="font-sm">小字体 (14px)</text>
                    <text class="font-base">基础字体 (16px)</text>
                    <text class="font-lg">大字体 (18px)</text>
                    <text class="font-xl">超大字体 (20px)</text>
                    <text class="font-xxl">特大字体 (22px)</text>
                </view>
            </view>
            
            <view class="test-card">
                <text class="card-title">间距测试</text>
                <view class="spacing-samples">
                    <view class="spacing-item spacing-xs">超小间距 (4px)</view>
                    <view class="spacing-item spacing-sm">小间距 (8px)</view>
                    <view class="spacing-item spacing-base">基础间距 (12px)</view>
                    <view class="spacing-item spacing-lg">大间距 (16px)</view>
                    <view class="spacing-item spacing-xl">超大间距 (24px)</view>
                </view>
            </view>
            
            <view class="test-card">
                <text class="card-title">按钮测试</text>
                <view class="button-samples">
                    <view class="btn-test responsive-button">响应式按钮</view>
                    <view class="btn-test responsive-button btn-primary">主要按钮</view>
                    <view class="btn-test responsive-button btn-secondary">次要按钮</view>
                </view>
            </view>
            
            <view class="test-card">
                <text class="card-title">头部高度测试</text>
                <view class="header-test">
                    <view class="test-header">
                        <view class="header-bg"></view>
                        <view class="back-btn">
                            <view class="back-arrow"></view>
                        </view>
                        <text class="title">测试标题</text>
                        <view class="button-group">
                            <view class="btn-test-header">测试按钮</view>
                        </view>
                    </view>
                    <text class="height-info">当前头部高度: {{ headerHeight }}px</text>
                </view>
            </view>

            <view class="test-card">
                <text class="card-title">表格测试</text>
                <view class="table-test responsive-table">
                    <view class="table-header">
                        <view class="table-cell header-cell">序号</view>
                        <view class="table-cell header-cell">患者信息</view>
                        <view class="table-cell header-cell">测评项目</view>
                        <view class="table-cell header-cell">状态</view>
                        <view class="table-cell header-cell">操作</view>
                    </view>
                    <view class="table-body">
                        <view class="table-row">
                            <view class="table-cell">1</view>
                            <view class="table-cell">张三 男/8岁</view>
                            <view class="table-cell">SNAP-IV父母评定量表</view>
                            <view class="table-cell">已完成</view>
                            <view class="table-cell">查看报告</view>
                        </view>
                        <view class="table-row">
                            <view class="table-cell">2</view>
                            <view class="table-cell">李四 女/7岁</view>
                            <view class="table-cell">PPVT测评</view>
                            <view class="table-cell">执行中</view>
                            <view class="table-cell">继续测评</view>
                        </view>
                    </view>
                </view>
                <text class="height-info">当前表头高度: {{ tableHeaderHeight }}px</text>
            </view>
        </view>
        
        <!-- 刷新按钮 -->
        <view class="refresh-btn" @click="refreshInfo">
            <text class="btn-text">刷新设备信息</text>
        </view>
    </view>
</template>

<script>
import { initResponsive, getDeviceInfo, calculateScale, responsiveMixin } from '@/utils/responsive.js'

export default {
    mixins: [responsiveMixin],
    data() {
        return {
            currentTime: '9:41',
            deviceInfo: {
                windowWidth: 0,
                windowHeight: 0,
                pixelRatio: 1,
                screenWidth: 0,
                screenHeight: 0
            },
            scaleInfo: {
                finalScale: 1,
                is11InchScreen: false,
                sizeScale: 1,
                resolutionScale: 1,
                dpiScale: 1
            },
            physicalSize: {
                width: 0,
                height: 0,
                diagonal: 0
            },
            headerHeight: 82,
            headerHeightRecord: 88,
            tableHeaderHeight: 60
        }
    },
    async onShow() {
        //#ifdef APP-PLUS
        plus.navigator.setFullscreen(true);
        //#endif
        
        await this.refreshInfo();
        this.updateTime();
    },
    methods: {
        async refreshInfo() {
            try {
                const deviceInfo = await getDeviceInfo();
                this.deviceInfo = deviceInfo;
                
                const scaleInfo = calculateScale(deviceInfo);
                this.scaleInfo = scaleInfo;
                
                // 计算物理尺寸
                const physicalWidth = deviceInfo.windowWidth / (96 * deviceInfo.pixelRatio);
                const physicalHeight = deviceInfo.windowHeight / (96 * deviceInfo.pixelRatio);
                const diagonal = Math.sqrt(physicalWidth * physicalWidth + physicalHeight * physicalHeight);
                
                this.physicalSize = {
                    width: physicalWidth.toFixed(2),
                    height: physicalHeight.toFixed(2),
                    diagonal: diagonal.toFixed(2)
                };

                // 计算头部和表头高度
                const is11Inch = scaleInfo.is11InchScreen;
                this.headerHeight = is11Inch ? 41 : 82;
                this.headerHeightRecord = is11Inch ? 44 : 88;
                this.tableHeaderHeight = is11Inch ? 30 : 60;

                console.log('设备信息更新:', {
                    deviceInfo: this.deviceInfo,
                    scaleInfo: this.scaleInfo,
                    physicalSize: this.physicalSize,
                    heights: {
                        headerHeight: this.headerHeight,
                        headerHeightRecord: this.headerHeightRecord,
                        tableHeaderHeight: this.tableHeaderHeight
                    }
                });

                // 重新初始化响应式适配
                await initResponsive();
                
            } catch (error) {
                console.error('获取设备信息失败:', error);
            }
        },
        updateTime() {
            const now = new Date();
            this.currentTime = now.getHours().toString().padStart(2, '0') + ':' + 
                             now.getMinutes().toString().padStart(2, '0');
        },
        goBack() {
            uni.navigateBack();
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/styles/responsive.scss';

.responsive-test-page {
    width: 100vw;
    height: 100vh;
    background: #F6F6F6;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
}

/* 状态栏 */
.status-bar {
    width: 100%;
    height: 48px;
    background: #FFFFFF;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 72px 0 72px;
    flex-shrink: 0;
    
    .time {
        font-family: 'Alibaba PuHuiTi';
        font-size: 28px;
        color: #333333;
        line-height: 16px;
    }
    
    .status-icons {
        display: flex;
        align-items: center;
        gap: 56px;
    }
}

/* 顶部功能区 */
.header {
    position: relative;
    width: 100%;
    height: 88px;
    flex-shrink: 0;
    
    .header-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #FFFFFF;
    }
    
    .back-btn {
        position: absolute;
        left: 40px;
        top: 28px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        
        .back-arrow {
            width: 9.33px;
            height: 18.67px;
            position: relative;
            
            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 9.33px;
                height: 18.67px;
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9.33 18.67'%3E%3Cpath d='M8.33 1L1 9.33l7.33 8.34' stroke='%23333333' stroke-width='3' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center;
                background-size: contain;
            }
        }
    }
    
    .title {
        position: absolute;
        left: 50%;
        top: 28px;
        transform: translateX(-50%);
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        color: #333333;
        line-height: 32px;
    }
}

/* 设备信息区域 */
.device-info {
    margin: 20px 40px;
    flex-shrink: 0;
}

.info-card,
.test-card {
    background: #FFFFFF;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    
    .card-title {
        font-family: 'Alibaba PuHuiTi';
        font-size: 24px;
        color: #333333;
        font-weight: bold;
        margin-bottom: 16px;
        display: block;
    }
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #F0F0F0;
    
    &:last-child {
        border-bottom: none;
    }
    
    .label {
        font-family: 'Alibaba PuHuiTi';
        font-size: 20px;
        color: #666666;
    }
    
    .value {
        font-family: 'Alibaba PuHuiTi';
        font-size: 20px;
        color: #333333;
        font-weight: bold;
        
        &.highlight {
            color: #287FFF;
            background: #EBF2FF;
            padding: 4px 8px;
            border-radius: 4px;
        }
    }
}

/* 测试区域 */
.test-area {
    flex: 1;
    margin: 0 40px;
    overflow-y: auto;
    min-height: 0;
}

/* 字体测试 */
.font-samples {
    display: flex;
    flex-direction: column;
    gap: 12px;
    
    .font-xs { font-size: var(--font-size-xs); }
    .font-sm { font-size: var(--font-size-sm); }
    .font-base { font-size: var(--font-size-base); }
    .font-lg { font-size: var(--font-size-lg); }
    .font-xl { font-size: var(--font-size-xl); }
    .font-xxl { font-size: var(--font-size-xxl); }
}

/* 间距测试 */
.spacing-samples {
    display: flex;
    flex-direction: column;
    
    .spacing-item {
        background: #F0F0F0;
        border: 1px solid #E0E0E0;
        border-radius: 4px;
        font-size: 16px;
        color: #333333;
        margin-bottom: 8px;
        
        &.spacing-xs { padding: var(--spacing-xs); }
        &.spacing-sm { padding: var(--spacing-sm); }
        &.spacing-base { padding: var(--spacing-base); }
        &.spacing-lg { padding: var(--spacing-lg); }
        &.spacing-xl { padding: var(--spacing-xl); }
    }
}

/* 按钮测试 */
.button-samples {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
    
    .btn-test {
        cursor: pointer;
        
        &.btn-primary {
            background: #287FFF;
            color: #FFFFFF;
        }
        
        &.btn-secondary {
            background: #FFFFFF;
            color: #333333;
            border: 1px solid #C7C7C7;
        }
    }
}

/* 头部测试 */
.header-test {
    .test-header {
        position: relative;
        width: 100%;
        height: var(--header-height, 82px);
        margin-bottom: 12px;

        .header-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #287FFF;
            border-radius: 8px;
        }

        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            .back-arrow {
                width: 8px;
                height: 16px;
                position: relative;

                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 8px;
                    height: 16px;
                    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 16'%3E%3Cpath d='M7 1L1 8l6 7' stroke='%23FFFFFF' stroke-width='2' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center;
                    background-size: contain;
                }
            }
        }

        .title {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translateX(-50%) translateY(-50%);
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #FFFFFF;
            line-height: 1;
        }

        .button-group {
            position: absolute;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);

            .btn-test-header {
                padding: 8px 16px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 4px;
                font-size: 16px;
                color: #FFFFFF;
                cursor: pointer;
            }
        }
    }

    .height-info {
        font-size: 14px;
        color: #666666;
        text-align: center;
        display: block;
    }
}

/* 表格测试 */
.table-test {
    .table-header {
        display: flex;
        background: #EEEEEE;
        min-height: var(--table-header-height, 60px);
    }

    .table-body {
        .table-row {
            display: flex;

            &:hover {
                background: #F8F9FA;
            }
        }
    }

    .table-cell {
        flex: 1;
        padding: 12px;
        border-right: 1px solid #EEEEEE;
        border-bottom: 1px solid #EEEEEE;
        font-size: 16px;
        color: #333333;
        display: flex;
        align-items: center;
        justify-content: center;

        &:last-child {
            border-right: none;
        }

        &.header-cell {
            font-weight: bold;
            background: #EEEEEE;
            min-height: var(--table-header-height, 60px);
            padding: 6px 10px;
        }
    }
}

.height-info {
    font-size: 14px;
    color: #666666;
    text-align: center;
    display: block;
    margin-top: 8px;
}

/* 刷新按钮 */
.refresh-btn {
    margin: 20px 40px;
    padding: 16px;
    background: #287FFF;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    flex-shrink: 0;
    
    .btn-text {
        font-family: 'Alibaba PuHuiTi';
        font-size: 20px;
        color: #FFFFFF;
    }
    
    &:active {
        opacity: 0.8;
    }
}
</style>
