<template>
	<view class="record">
		<!-- 顶部功能区 -->
		<view class="header-container">
			<!-- 首页区域 -->
			<view class="home-section" @click="go">
				<image class="home-icon" src="/static/patients/patient-home-icon.png" mode="aspectFit"></image>
				<text class="home-text">首页</text>
			</view>
			
			<!-- 搜索框 -->
			<view class="search-container">
				<input 
					type="text" 
					placeholder="请输入病案号或姓名" 
					v-model="state.param.param" 
					class="search-input" 
				/>
				<view class="search-addon" @click="confirm">
					<image class="search-icon" src="/static/patients/patient-search-icon.png" mode="aspectFit"></image>
				</view>
			</view>
			
			<!-- 添加患者按钮 -->
			<view class="add-patient-btn" @click="goAdd">
				<image class="upload-icon" src="/static/patients/patient-add-icon.png" mode="aspectFit"></image>
				<text class="add-text">添加患者</text>
			</view>
		</view>

		<!-- 表格+分页 -->
		<view class="table-container">
			<!-- 表格 -->
			<view class="table-wrapper">
				<!-- 表头 -->
				<view class="table-header">
					<view v-for="(item,index) in title" :key="index" class="table-header-cell" :style="{width: item.width}">
						{{item.name}}
					</view>
				</view>
				
				<!-- 表格内容 -->
				<view class="table-body">
					<!-- 空状态 -->
					<view class="empty-state" v-if="!state.list.length">
						<view class="empty-icon">
							<text class="iconfont">&#xe678;</text>
						</view>
						<text class="empty-text">暂无就诊人信息</text>
					</view>
					
					<!-- 数据行 -->
					<view v-else>
						<view v-for="(item,index) in state.list" :key="index" class="table-row">
							<view class="table-cell" :style="{width: title[0].width}">{{(state.pageIndex - 1) * state.pageSize + index + 1}}</view>
							<view class="table-cell" :style="{width: title[1].width}">{{item.serNum}}</view>
							<view class="table-cell" :style="{width: title[2].width}">{{item.patientName}}</view>
							<view class="table-cell" :style="{width: title[3].width}">{{item.sex==='M'?'男':'女'}}</view>
							<view class="table-cell" :style="{width: title[4].width}">{{item.age}}</view>
							<view class="table-cell" :style="{width: title[5].width}">{{getSch(item.stanOfCul)}}</view>
							<view class="table-cell" :style="{width: title[6].width}">{{item.remark||'未填写'}}</view>
							<view class="table-cell" :style="{width: title[7].width}"></view>
							<view class="table-cell" :style="{width: title[8].width}"></view>
							<view class="table-cell action-cell" :style="{width: title[9].width}">
								<text class="action-link edit-link" @click="()=>goEdit(item.outpatientId)">编辑</text>
								<text class="action-link test-link" @click="()=>goTest(item.outpatientId,item.patientName,item.year)">测评</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 分页 -->
			<view class="pagination">
				<view class="pagination-info">
					<text>共 {{state.total}} 条</text>
				</view>
				<view class="pagination-controls">
					<button class="page-btn" :disabled="state.pageIndex <= 1" @click="prevPage">
						<image class="page-icon" src="/static/patients/page-left-icon.png" mode="aspectFit"></image>
					</button>
					<view class="page-numbers">
						<button v-for="page in visiblePages" :key="page" 
							class="page-number" 
							:class="{active: page === state.pageIndex}"
							@click="goToPage(page)">
							{{page}}
						</button>
					</view>
					<button class="page-btn" :disabled="state.pageIndex >= totalPages" @click="nextPage">
						<image class="page-icon" src="/static/patients/page-right-icon.png" mode="aspectFit"></image>
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		reactive,
		watch,
		computed
	} from 'vue';
	import {
		navigateTo,
		redirectTo,
		reLaunch,
		showLoading,
		hideLoading,
		getStorageSync
	} from '@/common/uniTool'
	import {
		getSch
	} from '../../common/method'
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		qryPatientListPage,
		qryTraineeInfoListPage
	} from '../../service';
	import {
		useUserStore
	} from '../../stores/user';
	
	const userStore = useUserStore()
	const state = reactive({
		pageIndex: 1,
		pageSize: 10,
		status: 'more',
		total: 0,
		param: {
			param: ''
		},
		list: []
	})
	
	// 按照Figma比例重新计算，填满整个屏幕宽度
	const title = [{
			name: '序号',
			width: '4.6%'
		},
		{
			name: '病案号',
			width: '15%'
		},
		{
			name: '姓名',
			width: '9.3%'
		},
		{
			name: '性别',
			width: '9.3%'
		},
		{
			name: '年龄',
			width: '9.3%'
		},
		{
			name: '教育程度',
			width: '10.6%'
		},
		{
			name: '临床诊断',
			width: '10.5%'
		},
		{
			name: '工单记录',
			width: '10.5%'
		},
		{
			name: '测评记录',
			width: '11.6%'
		},
		{
			name: '操作',
			width: '15%'
		}
	]

	onShow(() => {
		// #ifdef APP-PLUS
		// 设置状态栏为深色字体并确保显示
		plus.navigator.setStatusBarStyle('dark');
		plus.navigator.setStatusBarBackground('#FFFFFF');
		plus.screen.lockOrientation('landscape-primary');
		// #endif
		
		state.pageIndex = 1
		state.list = []
		getList()
	})
	
	// 计算属性
	const totalPages = computed(() => {
		return Math.ceil(state.total / state.pageSize)
	})
	
	const visiblePages = computed(() => {
		const current = state.pageIndex
		const total = totalPages.value
		const pages = []
		
		if (total <= 7) {
			// 如果总页数小于等于7，显示所有页数
			for (let i = 1; i <= total; i++) {
				pages.push(i)
			}
		} else {
			// 如果总页数大于7，显示部分页数
			if (current <= 4) {
				for (let i = 1; i <= 5; i++) {
					pages.push(i)
				}
				pages.push('...')
				pages.push(total)
			} else if (current >= total - 3) {
				pages.push(1)
				pages.push('...')
				for (let i = total - 4; i <= total; i++) {
					pages.push(i)
				}
			} else {
				pages.push(1)
				pages.push('...')
				for (let i = current - 1; i <= current + 1; i++) {
					pages.push(i)
				}
				pages.push('...')
				pages.push(total)
			}
		}
		
		return pages
	})
	
	const goTest = (outpatientId, patientName, year) => {
		userStore.outpatientId = outpatientId
		userStore.userName = patientName
		userStore.year = year
		navigateTo('/pages/home/<USER>')
	}
	
	const goEdit = (outpatientId) => {
		navigateTo(`/pages/index/index?outpatientId=${outpatientId}`)
	}
	
	const getList = async () => {
		state.status = 'loading'
		let res = await qryTraineeInfoListPage({
			param: state.param,
			pageSize: state.pageSize,
			pageIndex: state.pageIndex
		})
		
		// 字段映射处理
		state.list = res.data.map(item => ({
			// 原字段 -> 新字段映射
			serNum: item.medicalRecordNo, // medicalRecordNo -> serNum
			outpatientId: item.traineeId, // traineeId -> outpatientId
			patientName: item.traineeName, // traineeName -> patientName
			sex: item.sex, // 保持不变
			age: item.age, // 保持不变
			year: extractYearFromAge(item.age), // 从age字段提取year
			stanOfCul: item.stanOfCul, // 保持不变
			remark: item.desc || '', // desc -> remark
			grade: '', // 新接口没有grade字段，设为空字符串
			updateDate: item.updateDate || '' // 新接口没有updateDate字段，设为空字符串
		}))
		
		state.total = res.total
		state.status = "more"
	}
	
	// 从age字段提取year的函数
	const extractYearFromAge = (ageStr) => {
		if (!ageStr) return 0
		// 从 "4岁7月" 这样的字符串中提取数字
		const yearMatch = ageStr.match(/(\d+)岁/)
		return yearMatch ? parseInt(yearMatch[1]) : 0
	}
	
	const confirm = () => {
		state.pageIndex = 1
		state.list = []
		getList()
	}
	
	const goAdd = () => {
		navigateTo('/pages/index/index')
	}
	
	const go = () => {
		reLaunch('/pages/home/<USER>')
	}
	
	// 分页方法
	const prevPage = () => {
		if (state.pageIndex > 1) {
			state.pageIndex--
			state.list = []
			getList()
		}
	}
	
	const nextPage = () => {
		if (state.pageIndex < totalPages.value) {
			state.pageIndex++
			state.list = []
			getList()
		}
	}
	
	const goToPage = (page) => {
		if (page !== '...' && page !== state.pageIndex) {
			state.pageIndex = page
			state.list = []
			getList()
		}
	}
</script>

<style lang="scss">
	.record {
		width: 100vw;
		height: 100vh;
		background: #F6F6F6;
		display: flex;
		flex-direction: column;
		overflow: hidden;
		padding-top: var(--status-bar-height);

		// 顶部功能区 - 按照Figma设计2倍尺寸
		.header-container {
			height: 176rpx;
			background: #FFFFFF;
			display: flex;
			align-items: center;
			padding: 0 64rpx;
			position: relative;

			// 首页区域 - 2倍尺寸
			.home-section {
				display: flex;
				align-items: center;
				gap: 16rpx;
				position: absolute;
				left: 64rpx;
				cursor: pointer;

				.home-icon {
					width: 80rpx;
					height: 80rpx;
				}

				.home-text {
					font-family: 'Alibaba PuHuiTi';
					font-weight: 400;
					font-size: 48rpx;
					line-height: 48rpx;
					color: #333333;
				}
			}

			// 搜索框右移 - 2倍尺寸
			.search-container {
				position: absolute;
				right: 390rpx; // 64rpx(添加患者按钮right) + 296rpx(添加患者按钮宽度) + 30rpx(间距)
				width: 648rpx;
				height: 104rpx;
				border: 2rpx solid #EEEEEE;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				background: #FFFFFF;
				overflow: hidden;

				.search-input {
					flex: 1;
					height: 100%;
					border: none;
					outline: none;
					padding: 0 24rpx;
					font-family: 'Alibaba PuHuiTi';
					font-weight: 400;
					font-size: 32rpx;
					line-height: 44rpx;
					color: #333333;
					background: transparent;

					&::placeholder {
						color: #C7C7C7;
					}
				}

				.search-addon {
					width: 104rpx;
					height: 104rpx;
					border-left: 2rpx solid #EEEEEE;
					background: #FFFFFF;
					display: flex;
					align-items: center;
					justify-content: center;
					cursor: pointer;
					border-radius: 0 16rpx 16rpx 0;

					.search-icon {
						width: 40rpx;
						height: 40rpx;
					}
				}
			}

			// 添加患者按钮 - 2倍尺寸
			.add-patient-btn {
				position: absolute;
				right: 64rpx;
				display: flex;
				align-items: center;
				gap: 16rpx;
				padding: 32rpx;
				border: 2rpx solid #EEEEEE;
				border-radius: 16rpx;
				background: #FFFFFF;
				cursor: pointer;

				&:hover {
					background: #F8F9FA;
				}

				.upload-icon {
					width: 40rpx;
					height: 40rpx;
				}

				.add-text {
					font-family: 'Alibaba PuHuiTi';
					font-weight: 500;
					font-size: 40rpx;
					line-height: 40rpx;
					color: #333333;
					width: 176rpx;
					text-align: center;
				}
			}
		}

		// 表格容器
		.table-container {
			flex: 1;
			margin: 10rpx 40rpx 15rpx 40rpx;
			background: #FFFFFF;
			border-radius: 16rpx;
			display: flex;
			flex-direction: column;
			overflow: hidden;

			.table-wrapper {
				flex: 1;
				display: flex;
				flex-direction: column;
				overflow: hidden;
				width: 100%;
			}

			// 表头
			.table-header {
				height: 132rpx;
				background: #EEEEEE;
				display: flex;
				align-items: center;
				font-weight: normal;
				font-size: 42rpx;
				color: #333333;

				&-cell {
					display: flex;
					align-items: center;
					justify-content: center;
					text-align: center;
					padding: 24rpx 18rpx;
					border-bottom: 1rpx solid #EEEEEE;
					border-left: 1rpx solid #EEEEEE;
					min-height: 132rpx;

					&:first-child {
						border-left: none;
					}

					&:last-child {
						justify-content: flex-start;
					}
				}
			}

			// 表格主体
			.table-body {
				flex: 1;
				overflow-y: auto;

				// 空状态
				.empty-state {
					height: 100%;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					color: #999999;

					.empty-icon {
						width: 180rpx;
						height: 180rpx;
						background: #F9F9F9;
						border: 3rpx solid #EAEAEA;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-bottom: 36rpx;

						.iconfont {
							font-size: 90rpx;
							color: #EAEAEA;
						}
					}

					.empty-text {
						font-size: 42rpx;
					}
				}

				// 表格行
				.table-row {
					height: 117rpx;
					display: flex;
					align-items: center;
					border-bottom: 1rpx solid #EEEEEE;
					font-size: 42rpx;
					color: #333333;

					&:hover {
						background: #F8F9FA;
					}

					&:last-child {
						border-bottom: none;
					}

					.table-cell {
						display: flex;
						align-items: center;
						justify-content: center;
						text-align: center;
						padding: 13rpx 18rpx;
						border-bottom: 1rpx solid #EEEEEE;
						border-left: 1rpx solid #EEEEEE;
						min-height: 117rpx;
						word-break: break-all;
						background: #FFFFFF;

						&:first-child {
							border-left: none;
						}

						&:last-child {
							justify-content: flex-start;
							padding-left: 18rpx;
						}

						&.action-cell {
							gap: 36rpx;
							justify-content: flex-start;
							padding-left: 18rpx;
							box-shadow: -6rpx 0 6rpx -6rpx rgba(0, 0, 0, 0.06);
						}

						.action-link {
							font-size: 42rpx;
							color: #1890FF;
							cursor: pointer;
							line-height: 1;
							text-decoration: none;
							background: none;
							border: none;
							padding: 0;

							&:hover {
								color: #40A9FF;
								text-decoration: underline;
							}

							&.edit-link {
								color: #1890FF;
							}

							&.test-link {
								color: #1890FF;
							}
						}
					}
				}
			}

			// 分页
			.pagination {
				height: 120rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 15rpx 24rpx;
				border-top: 1rpx solid #EEEEEE;
				font-size: 36rpx;
				color: #666666;
				margin-bottom: 15rpx;

				.pagination-info {
					display: flex;
					align-items: center;
					height: 90rpx;
					line-height: 90rpx;
				}

				.pagination-controls {
					display: flex;
					align-items: center;
					gap: 24rpx;
					height: 90rpx;

					.page-btn {
						width: 90rpx;
						height: 90rpx;
						border: 1rpx solid #D9D9D9;
						border-radius: 6rpx;
						background: #FFFFFF;
						display: flex;
						align-items: center;
						justify-content: center;
						cursor: pointer;
						color: #666666;

						&:hover:not(:disabled) {
							border-color: #3481FD;
							color: #3481FD;
						}

						&:disabled {
							background: #F5F5F5;
							color: #CCCCCC;
							cursor: not-allowed;
						}

						.page-icon {
							width: 42rpx;
							height: 42rpx;
						}
					}

					.page-numbers {
						display: flex;
						align-items: center;
						gap: 12rpx;

						.page-number {
							min-width: 90rpx;
							height: 90rpx;
							border: 1rpx solid #D9D9D9;
							border-radius: 6rpx;
							background: #FFFFFF;
							display: flex;
							align-items: center;
							justify-content: center;
							cursor: pointer;
							color: #666666;
							font-size: 36rpx;
							padding: 0 12rpx;

							&:hover {
								border-color: #3481FD;
								color: #3481FD;
							}

							&.active {
								background: #3481FD;
								border-color: #3481FD;
								color: #FFFFFF;
							}
						}
					}
				}
			}
		}
	}
</style>