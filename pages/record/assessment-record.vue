<template>
    <view class="assessment-record-page responsive-container" @click="closeAllDropdowns">
        <!-- 状态栏 -->
        <view class="status-bar">
            <view class="time">9:41</view>
            <view class="status-icons">
                <!-- 状态栏图标区域 -->
            </view>
        </view>
        
        <!-- 顶部功能区 -->
        <view class="header">
            <view class="header-bg"></view>
            <view class="back-btn" @click="goBack">
                <view class="back-arrow"></view>
            </view>
            <text class="title">测评记录</text>
        </view>
        
        <!-- 筛选区域 -->
        <view class="filter-section">
            <!-- 搜索框 -->
            <view class="search-frame">
                <input type="text" v-model="searchText" placeholder="请输入患者名字/病案号查找" class="search-input" />
                <view class="search-addon">
                    <view class="search-icon"></view>
                </view>
            </view>
            
            <!-- 测评项目下拉 -->
            <view class="filter-select project-select" @click.stop="toggleProjectDropdown">
                <text class="filter-text" :class="{ active: !!filters.project }">{{ projectDisplayText }}</text>
                <view class="arrow-down"></view>
                <view class="dropdown" v-if="showProjectDropdown" @click.stop>
                    <view class="dropdown-item" :class="{ selected: !filters.project }" @click="selectProject('')">测评项目</view>
                    <view class="dropdown-item" :class="{ selected: filters.project === 'SNAP-IV' }" @click="selectProject('SNAP-IV')">SNAP-IV父母评定量表(26项)</view>
                    <view class="dropdown-item" :class="{ selected: filters.project === 'VADRS' }" @click="selectProject('VADRS')">VanderbiltADHD诊断评定量表(VADRS)</view>
                    <view class="dropdown-item" :class="{ selected: filters.project === 'BRIEF-P' }" @click="selectProject('BRIEF-P')">学龄前执行功能行为评定问卷(BRIEF-P)</view>
                    <view class="dropdown-item" :class="{ selected: filters.project === 'BRIEF' }" @click="selectProject('BRIEF')">学龄儿童执行功能行为评定问卷(BRIEF)</view>
                    <view class="dropdown-item" :class="{ selected: filters.project === 'Stroop' }" @click="selectProject('Stroop')">Stroop测验</view>
                </view>
            </view>
            
            <!-- 性别下拉 -->
            <view class="filter-select gender-select" @click.stop="toggleGenderDropdown">
                <text class="filter-text" :class="{ active: !!filters.gender }">{{ genderDisplayText }}</text>
                <view class="arrow-down"></view>
                <view class="dropdown" v-if="showGenderDropdown" @click.stop>
                    <view class="dropdown-item" :class="{ selected: !filters.gender }" @click="selectGender('')">性别</view>
                    <view class="dropdown-item" :class="{ selected: filters.gender === 'M' }" @click="selectGender('M')">男</view>
                    <view class="dropdown-item" :class="{ selected: filters.gender === 'F' }" @click="selectGender('F')">女</view>
                </view>
            </view>
            
            <!-- 完成时间选择器 -->
            <view class="date-picker" @click.stop="openDatePopover">
                <text class="date-text" :class="{ active: !!filters.completeDate }">{{ filters.completeDate || '完成时间' }}</text>
                <view class="calendar-icon-wrapper">
                    <view class="calendar-icon"></view>
                </view>
            </view>
            
            <!-- 来源下拉 -->
            <view class="filter-select source-select" @click.stop="toggleSourceDropdown">
                <text class="filter-text" :class="{ active: !!filters.source }">{{ sourceDisplayText }}</text>
                <view class="arrow-down"></view>
                <view class="dropdown" v-if="showSourceDropdown" @click.stop>
                    <view class="dropdown-item" :class="{ selected: !filters.source }" @click="selectSource('')">来源</view>
                    <view class="dropdown-item" :class="{ selected: filters.source === 'workorder' }" @click="selectSource('workorder')">测评工单</view>
                    <view class="dropdown-item" :class="{ selected: filters.source === 'individual' }" @click="selectSource('individual')">单独测评</view>
                </view>
            </view>
            
            <!-- 得分范围 -->
            <view class="score-range">
                <view class="score-input">
                    <input type="number" v-model="filters.minScore" placeholder="最小得分：请输入" class="score-field" />
                </view>
                <text class="range-separator">至</text>
                <view class="score-input">
                    <input type="number" v-model="filters.maxScore" placeholder="最大得分：请输入" class="score-field" />
                </view>
            </view>
            
            <!-- 年龄范围 -->
            <view class="age-range">
                <view class="age-input">
                    <input type="number" v-model="filters.minAge" placeholder="最小年龄：请输入" class="age-field" />
                </view>
                <text class="range-separator">至</text>
                <view class="age-input">
                    <input type="number" v-model="filters.maxAge" placeholder="最大年龄：请输入" class="age-field" />
                </view>
            </view>
            
            <!-- 按钮组 -->
            <view class="button-group">
                <view class="btn-reset" @click="onReset">重置</view>
                <view class="btn-search" @click="onSearch">搜索</view>
            </view>
        </view>
        
        <!-- 表格区域 -->
        <view class="table-section">
            <!-- 表格标题 -->
            <view class="table-title">
                <text class="title-text">测评工单列表</text>
            </view>
            
            <!-- 表格容器 -->
            <view class="table-container">
                <!-- 表格头部 -->
                <view class="table-header">
                    <view class="table-cell header-cell col-index">序号</view>
                    <view class="table-cell header-cell col-patient">患者信息</view>
                    <view class="table-cell header-cell col-project">测评项目</view>
                    <view class="table-cell header-cell col-time">完成时间</view>
                    <view class="table-cell header-cell col-result">结论</view>
                    <view class="table-cell header-cell col-source">来源</view>
                    <view class="table-cell header-cell col-action">操作</view>
                </view>
                
                <!-- 表格内容 -->
                <view class="table-body">
                    <view class="table-row" v-for="(item, index) in tableData" :key="item.id || index">
                        <view class="table-cell col-index">{{ index + 1 }}</view>
                        <view class="table-cell col-patient">
                            <text class="patient-name">{{ item.patientName }} {{ item.gender === 'M' ? '男' : '女' }}/{{ item.age }}岁{{ item.months }}个月/{{ item.patientId }}</text>
                        </view>
                        <view class="table-cell col-project">
                            <view class="project-content">
                                <text class="project-name">{{ item.projectName }}</text>
                                <view class="status-tag" :class="item.status" v-if="item.status === 'canceled'">已撤销</view>
                            </view>
                        </view>
                        <view class="table-cell col-time">{{ item.completeTime }}</view>
                        <view class="table-cell col-result">{{ item.score }}分</view>
                        <view class="table-cell col-source">
                            <view class="source-content">
                                <text class="source-text">{{ item.source }}</text>
                                <view class="detail-btn" v-if="item.source === '测评工单'">详情</view>
                            </view>
                        </view>
                        <view class="table-cell col-action">
                            <view class="action-btns">
                                <text class="action-btn" @click="viewReport(item)">查看报告</text>
                                <view class="btn-divider"></view>
                                <text class="action-btn" @click="retestAssessment(item)">再次测评</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            
            <!-- 分页 -->
            <view class="pagination">
                <view class="pagination-left">
                    <text class="total-text">共{{ totalCount }}条</text>
                </view>
                <view class="pagination-controls">
                    <view class="page-size-selector">
                        <text class="page-size-text">{{ pageSize }}条/页</text>
                        <view class="page-size-arrow"></view>
                    </view>
                    <view class="page-btn" :class="{ disabled: pageIndex <= 1 }" @click="prevPage">
                        <view class="arrow-left"></view>
                    </view>
                    <view class="page-numbers">
                        <template v-for="page in visiblePages" :key="page">
                            <view v-if="page === '...'" class="page-ellipsis">···</view>
                            <view v-else class="page-number" :class="{ active: page === pageIndex }" @click="goToPage(page)">{{ page }}</view>
                        </template>
                    </view>
                    <view class="page-btn" :class="{ disabled: pageIndex >= totalPages }" @click="nextPage">
                        <view class="arrow-right"></view>
                    </view>
                </view>
                <view class="pagination-right">
                    <text class="goto-text">前往</text>
                    <view class="goto-input">
                        <input type="number" v-model="gotoPageInput" @confirm="gotoPage" class="goto-input-field" />
                    </view>
                    <text class="goto-text">页</text>
                </view>
            </view>
        </view>
        
        <!-- 日期选择弹窗 -->
        <view v-if="showDatePopover" class="date-popover" @click.stop>
            <view class="popover-header">
                <view class="nav-btn" @click="prevMonth">‹</view>
                <view class="ym-text">{{ popoverYear }}年{{ popoverMonth }}月</view>
                <view class="nav-btn" @click="nextMonth">›</view>
            </view>
            <view class="week-row">
                <text v-for="w in ['日','一','二','三','四','五','六']" :key="w" class="week-cell">{{ w }}</text>
            </view>
            <view class="day-grid">
                <view v-for="(cell, idx) in calendarCells" :key="idx"
                      class="day-cell"
                      :class="{ other: cell.isOtherMonth, selected: cell.fullDate === selectedTempDate }"
                      @click="onSelectDate(cell)">
                    {{ cell.day }}
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { initResponsive, getCurrentScale, responsiveMixin } from '@/utils/responsive.js'

export default {
    mixins: [responsiveMixin],
    data() {
        return {
            searchText: '',
            filters: {
                project: '',
                gender: '',
                completeDate: '',
                source: '',
                minScore: '',
                maxScore: '',
                minAge: '',
                maxAge: ''
            },
            showProjectDropdown: false,
            showGenderDropdown: false,
            showSourceDropdown: false,
            showDatePopover: false,
            popoverDate: new Date(),
            selectedTempDate: '',
            pageIndex: 1,
            pageSize: 20,
            totalPages: 20,
            totalCount: 400,
            gotoPageInput: 1,
            tableData: [
                {
                    id: 1,
                    patientName: '赵小车',
                    gender: 'M',
                    age: 6,
                    months: 8,
                    patientId: '600005212',
                    projectName: 'SNAP-IV父母评定量表(26项)',
                    status: 'canceled',
                    completeTime: '2025-04-05 15:24:56',
                    score: 20,
                    source: '测评工单'
                },
                {
                    id: 2,
                    patientName: '吴磊',
                    gender: 'M',
                    age: 12,
                    months: 8,
                    patientId: '600005213',
                    projectName: 'VanderbiltADHD诊断评定量表(VADRS)(家长问卷)',
                    completeTime: '2025-04-05 15:24:56',
                    score: 20,
                    source: '测评工单'
                },
                {
                    id: 3,
                    patientName: '陈浩',
                    gender: 'M',
                    age: 16,
                    months: 8,
                    patientId: '600005214',
                    projectName: '学龄前执行功能行为评定问卷(BRIEF-P)',
                    completeTime: '2025-04-05 15:24:56',
                    score: 20,
                    source: '测评工单'
                },
                {
                    id: 4,
                    patientName: '吴小美',
                    gender: 'F',
                    age: 7,
                    months: 8,
                    patientId: '600005215',
                    projectName: '学龄儿童执行功能行为评定问卷(BRIEF)',
                    completeTime: '2025-04-05 15:24:56',
                    score: 20,
                    source: '测评工单'
                },
                {
                    id: 5,
                    patientName: '陈娜',
                    gender: 'F',
                    age: 8,
                    months: 8,
                    patientId: '600005216',
                    projectName: 'Stroop测验',
                    completeTime: '2025-04-05 15:24:56',
                    score: 20,
                    source: '测评工单'
                }
            ]
        }
    },
    async onShow() {
        //#ifdef APP-PLUS
        plus.navigator.setFullscreen(true);
        //#endif
        
        try {
            await initResponsive();
            console.log('页面响应式适配初始化完成, 当前缩放比例:', getCurrentScale());
        } catch (error) {
            console.error('响应式适配初始化失败:', error);
        }
    },
    computed: {
        popoverYear() { return this.popoverDate.getFullYear() },
        popoverMonth() { return this.popoverDate.getMonth() + 1 },
        calendarCells() { return this.getMonthCells(this.popoverDate) },
        projectDisplayText() {
            const map = {
                'SNAP-IV': 'SNAP-IV父母评定量表',
                'VADRS': 'VanderbiltADHD诊断评定量表',
                'BRIEF-P': '学龄前执行功能行为评定问卷',
                'BRIEF': '学龄儿童执行功能行为评定问卷',
                'Stroop': 'Stroop测验'
            }
            return map[this.filters.project] || '测评项目'
        },
        genderDisplayText() {
            const map = { 'M': '男', 'F': '女' }
            return map[this.filters.gender] || '性别'
        },
        sourceDisplayText() {
            const map = { 'workorder': '测评工单', 'individual': '单独测评' }
            return map[this.filters.source] || '来源'
        },
        visiblePages() {
            const current = this.pageIndex
            const total = this.totalPages
            const pages = []
            
            if (total <= 7) {
                for (let i = 1; i <= total; i++) {
                    pages.push(i)
                }
            } else {
                if (current <= 4) {
                    for (let i = 1; i <= 5; i++) {
                        pages.push(i)
                    }
                    pages.push('...')
                    pages.push(total)
                } else if (current >= total - 3) {
                    pages.push(1)
                    pages.push('...')
                    for (let i = total - 4; i <= total; i++) {
                        pages.push(i)
                    }
                } else {
                    pages.push(1)
                    pages.push('...')
                    for (let i = current - 1; i <= current + 1; i++) {
                        pages.push(i)
                    }
                    pages.push('...')
                    pages.push(total)
                }
            }
            
            return pages
        }
    },
    methods: {
        goBack() {
            uni.navigateBack()
        },
        closeAllDropdowns() {
            this.showProjectDropdown = false
            this.showGenderDropdown = false
            this.showSourceDropdown = false
            this.showDatePopover = false
        },
        toggleProjectDropdown() {
            this.showProjectDropdown = !this.showProjectDropdown
            if (this.showProjectDropdown) {
                this.showGenderDropdown = false
                this.showSourceDropdown = false
            }
        },
        selectProject(project) {
            this.filters.project = project
            this.showProjectDropdown = false
        },
        toggleGenderDropdown() {
            this.showGenderDropdown = !this.showGenderDropdown
            if (this.showGenderDropdown) {
                this.showProjectDropdown = false
                this.showSourceDropdown = false
            }
        },
        selectGender(gender) {
            this.filters.gender = gender
            this.showGenderDropdown = false
        },
        toggleSourceDropdown() {
            this.showSourceDropdown = !this.showSourceDropdown
            if (this.showSourceDropdown) {
                this.showProjectDropdown = false
                this.showGenderDropdown = false
            }
        },
        selectSource(source) {
            this.filters.source = source
            this.showSourceDropdown = false
        },
        openDatePopover() {
            this.selectedTempDate = this.filters.completeDate || this.formatDate(new Date())
            this.popoverDate = this.filters.completeDate ? new Date(this.filters.completeDate) : new Date()
            this.showDatePopover = true
        },
        formatDate(d) {
            const Y = d.getFullYear()
            const M = (d.getMonth() + 1).toString().padStart(2, '0')
            const D = d.getDate().toString().padStart(2, '0')
            return `${Y}-${M}-${D}`
        },
        getMonthCells(date) {
            const year = date.getFullYear()
            const month = date.getMonth()
            const firstDay = new Date(year, month, 1)
            const startWeek = firstDay.getDay()
            const daysInMonth = new Date(year, month + 1, 0).getDate()
            const prevDays = startWeek
            const cells = []
            
            if (prevDays > 0) {
                const prevLastDate = new Date(year, month, 0).getDate()
                for (let i = prevDays - 1; i >= 0; i--) {
                    const day = prevLastDate - i
                    const full = this.formatDate(new Date(year, month - 1, day))
                    cells.push({ day, fullDate: full, isOtherMonth: true })
                }
            }
            
            for (let d = 1; d <= daysInMonth; d++) {
                const full = this.formatDate(new Date(year, month, d))
                cells.push({ day: d, fullDate: full, isOtherMonth: false })
            }
            
            while (cells.length % 7 !== 0 || cells.length < 42) {
                const nextIndex = cells.length - (prevDays + daysInMonth) + 1
                const full = this.formatDate(new Date(year, month + 1, nextIndex))
                cells.push({ day: nextIndex, fullDate: full, isOtherMonth: true })
            }
            
            return cells
        },
        onSelectDate(cell) {
            this.selectedTempDate = cell.fullDate
            this.filters.completeDate = cell.fullDate
            this.showDatePopover = false
        },
        prevMonth() {
            const y = this.popoverDate.getFullYear()
            const m = this.popoverDate.getMonth()
            this.popoverDate = new Date(y, m - 1, 1)
        },
        nextMonth() {
            const y = this.popoverDate.getFullYear()
            const m = this.popoverDate.getMonth()
            this.popoverDate = new Date(y, m + 1, 1)
        },
        onReset() {
            this.searchText = ''
            this.filters = {
                project: '',
                gender: '',
                completeDate: '',
                source: '',
                minScore: '',
                maxScore: '',
                minAge: '',
                maxAge: ''
            }
        },
        onSearch() {
            console.log('搜索条件:', {
                searchText: this.searchText,
                filters: this.filters
            })
        },
        prevPage() {
            if (this.pageIndex > 1) {
                this.pageIndex--
            }
        },
        nextPage() {
            if (this.pageIndex < this.totalPages) {
                this.pageIndex++
            }
        },
        goToPage(page) {
            if (page === '...' || page === this.pageIndex) return
            this.pageIndex = page
        },
        gotoPage() {
            const page = parseInt(this.gotoPageInput)
            if (page >= 1 && page <= this.totalPages && page !== this.pageIndex) {
                this.pageIndex = page
            }
        },
        viewReport(item) {
            console.log('查看报告:', item)
            uni.showToast({
                title: '查看报告功能待实现',
                icon: 'none'
            })
        },
        retestAssessment(item) {
            console.log('再次测评:', item)
            uni.showToast({
                title: '再次测评功能待实现',
                icon: 'none'
            })
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/styles/responsive.scss';

.assessment-record-page {
    width: 100vw;
    height: 100vh;
    background: #F6F6F6;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
}

/* 状态栏 */
.status-bar {
    width: 100%;
    height: 48px;
    background: #FFFFFF;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 72px 0 72px;
    flex-shrink: 0;
    
    .time {
        font-family: 'Alibaba PuHuiTi';
        font-size: 28px;
        color: #333333;
        line-height: 16px;
    }
    
    .status-icons {
        display: flex;
        align-items: center;
        gap: 56px;
    }
}

/* 顶部功能区 */
.header {
    position: relative;
    width: 100%;
    height: 88px;
    flex-shrink: 0;
    
    .header-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #FFFFFF;
    }
    
    .back-btn {
        position: absolute;
        left: 40px;
        top: 28px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        
        .back-arrow {
            width: 9.33px;
            height: 18.67px;
            position: relative;
            
            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 9.33px;
                height: 18.67px;
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9.33 18.67'%3E%3Cpath d='M8.33 1L1 9.33l7.33 8.34' stroke='%23333333' stroke-width='3' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center;
                background-size: contain;
            }
        }
    }
    
    .title {
        position: absolute;
        left: 50%;
        top: 28px;
        transform: translateX(-50%);
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        color: #333333;
        line-height: 32px;
    }
}

/* 筛选区域 */
.filter-section {
    width: calc(100% - 80px);
    margin: 0 40px;
    margin-top: 20rpx; /* 与顶部增加20rpx间距 */
    background: #FFFFFF;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 16px;
    flex-shrink: 0;
    
    display: grid;
    /* 第一行：搜索(略长) + 项目 + 完成时间 + 分数范围 + 性别 */
    /* 第二行：年龄范围(占两列) + 来源 + 按钮组(占三列，右对齐) */
    /* 列宽：搜索(弹性) | 项目(缩小为标准218的一半≈109px) | 完成时间(218px) | 分数范围(原252的2倍=504px) | 性别(218的一半≈109px) | 预留列 */
    grid-template-columns: 1.2fr 200px 218px 504px 109px 218px;
    grid-template-rows: auto auto;
    gap: 20rpx; /* 横纵间距约20rpx */
    align-items: center;
    
    .search-frame {
        grid-column: 1; /* 搜索区域稍长（1.2fr） */
        grid-row: 1;
        display: flex;
        align-items: center;
        border: 1px solid #EEEEEE;
        border-radius: 4px;
        background: #FFFFFF;
        padding-left: 16px;
        
        .search-input {
            height: 48px;
            flex: 1;
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #333333;
            border: none;
            outline: none;
            background: transparent;
            
            &::placeholder {
                color: #999999;
                font-size: 24px;
            }
        }
        
        .search-addon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-left: 1px solid #EEEEEE;
            border-radius: 0 4px 4px 0;
            margin-left: 8px;
            
            .search-icon {
                width: 29.09px;
                height: 29.09px;
                position: relative;
                
                &::before {
                    content: '';
                    position: absolute;
                    top: 3.64px;
                    left: 3.64px;
                    width: 19.64px;
                    height: 19.64px;
                    border: 1.2px solid #666666;
                    border-radius: 50%;
                }
                
                &::after {
                    content: '';
                    position: absolute;
                    bottom: 3.64px;
                    right: 3.64px;
                    width: 5.09px;
                    height: 5.09px;
                    background: #666666;
                    transform: rotate(45deg);
                    border-radius: 1px;
                }
            }
        }
    }
    
    .filter-select {
        background: #FFFFFF;
        border: 1px solid #EEEEEE;
        border-radius: 4px;
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        cursor: pointer;
        
        .filter-text {
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #999999;
            line-height: 24px;
            
            &.active {
                color: #333333;
            }
        }
        
        .arrow-down {
            width: 24px;
            height: 24px;
            position: relative;
            
            &::after {
                content: '';
                position: absolute;
                top: 9px;
                left: 4.5px;
                width: 15px;
                height: 9px;
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 15 9'%3E%3Cpath d='M1 1l6.5 6.5L14 1' stroke='%23999999' stroke-width='2' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center;
                background-size: contain;
            }
        }
        
        .dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            background: #FFFFFF;
            border: 1px solid #EEEEEE;
            border-radius: 4px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
            margin-top: 8px;
            z-index: 20;
            max-height: 200px;
            overflow-y: auto;
            
            .dropdown-item {
                padding: 10px 12px;
                font-size: 24px;
                color: #333333;
                cursor: pointer;
                
                &.selected {
                    color: #287FFF;
                }
                
                &:hover {
                    background: #F6F6F6;
                }
            }
        }
    }
    
    .date-picker {
        background: #FFFFFF;
        border: 1px solid #EEEEEE;
        border-radius: 4px;
        padding: 12px 16px;
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        
        .date-text {
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #999999;
            line-height: 24px;
            
            &.active {
                color: #333333;
            }
        }
        
        .calendar-icon-wrapper {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            
            .calendar-icon {
                width: 18px;
                height: 18px;
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 18 18'%3E%3Crect x='3' y='3' width='12' height='12' rx='1' stroke='%23999999' stroke-width='1.2' fill='none'/%3E%3Cpath d='M6 1v4M12 1v4M3 7h12' stroke='%23999999' stroke-width='1.2'/%3E%3C/svg%3E") no-repeat center;
                background-size: contain;
            }
        }
    }
    
    .score-range,
    .age-range {
        display: flex;
        align-items: center;
        gap: 20rpx; /* 行内元素间距约20rpx */
        
        .score-input,
        .age-input {
            background: #FFFFFF;
            border: 1px solid #EEEEEE;
            border-radius: 4px;
            height: 48px; /* 与搜索输入框高度保持一致 */
            padding: 0 16px; /* 垂直内边距归零，居中显示 */
            flex: 1;
            display: flex;
            align-items: center;
            
            .score-field,
            .age-field {
                width: 100%;
                height: 48px; /* 与搜索输入框高度一致 */
                font-family: 'Alibaba PuHuiTi';
                font-size: 24px;
                color: #333333;
                border: none;
                outline: none;
                background: transparent;
                
                &::placeholder {
                    color: #999999;
                    font-size: 24px;
                }
            }
        }
        
        .range-separator {
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #333333;
            flex-shrink: 0;
        }
    }
    
    /* 第一行排布（相邻间距20rpx） */
    .project-select { grid-column: 2; grid-row: 1; }
    .date-picker { grid-column: 3; grid-row: 1; }
    .score-range { grid-column: 4; grid-row: 1; }
    .gender-select { grid-column: 5; grid-row: 1; }

    /* 第二行排布 */
    .age-range { grid-column: 1 / 3; grid-row: 2; }
    .source-select { grid-column: 3; grid-row: 2; }
    .button-group {
        grid-column: 4 / 7; /* 跨三列，位于第二行右侧 */
        grid-row: 2;
        display: flex;
        gap: 20rpx;
        justify-content: flex-end;
        
        .btn-reset {
            height: 48px; /* 与输入框保持一致 */
            padding: 0 24px;
            background: #FFFFFF;
            border: 1px solid #C7C7C7;
            border-radius: 8px;
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #333333;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
        
        .btn-search {
            height: 48px; /* 与输入框保持一致 */
            padding: 0 24px;
            background: #287FFF;
            border-radius: 8px;
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #FFFFFF;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
    }
}

/* 表格区域 */
.table-section {
    flex: 1;
    margin: 0 40px;
    background: #FFFFFF;
    border-radius: 16px 16px 0 0;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
    
    .table-title {
        padding: 20px 24px;
        border-bottom: 1px solid #EEEEEE;
        flex-shrink: 0;
        
        .title-text {
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #333333;
        }
    }
}

.table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
}

.table-header {
    display: flex;
    background: #EEEEEE;
    flex-shrink: 0;
    
    .header-cell {
        font-family: 'Alibaba PuHuiTi';
        font-weight: 400;
        font-size: 24px;
        color: #333333;
        background: #EEEEEE;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px 12px;
        border-bottom: 1px solid #EEEEEE;
        border-right: 1px solid #EEEEEE;
        
        &:last-child {
            border-right: none;
        }
    }
}

.table-body {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
    
    .table-row {
        display: flex;
        
        &:hover {
            background: #F8F9FA;
        }
    }
}

.table-cell {
    font-family: 'Alibaba PuHuiTi';
    font-size: 24px;
    color: #333333;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 9px 12px;
    border-bottom: 1px solid #EEEEEE;
    border-right: 1px solid #EEEEEE;
    min-height: 61px;
    
    &:last-child {
        border-right: none;
    }
}

/* 列宽设置 */
.col-index {
    width: 68px;
    flex-shrink: 0;
}

.col-patient {
    width: 379px;
    flex-shrink: 0;
    justify-content: flex-start;
    
    .patient-name {
        text-align: left;
        line-height: 1.2;
    }
}

.col-project {
    width: 554px;
    flex-shrink: 0;
    justify-content: flex-start;
    
    .project-content {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        
        .project-name {
            color: #287FFF;
            flex: 1;
        }
        
        .status-tag {
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 24px;
            line-height: 1;
            flex-shrink: 0;
            
            &.canceled {
                background: #FFEFD2;
                color: #FFB130;
            }
        }
    }
}

.col-time {
    width: 265px;
    flex-shrink: 0;
}

.col-result {
    width: 118px;
    flex-shrink: 0;
}

.col-source {
    width: 200px;
    flex-shrink: 0;
    justify-content: flex-start;
    
    .source-content {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        
        .source-text {
            flex: 1;
        }
        
        .detail-btn {
            color: #287FFF;
            cursor: pointer;
            font-size: 24px;
            
            &:hover {
                text-decoration: underline;
            }
        }
    }
}

.col-action {
    width: 283px;
    flex-shrink: 0;
    justify-content: flex-start;
    
    .action-btns {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        
        .action-btn {
            color: #287FFF;
            cursor: pointer;
            font-size: 24px;
            
            &:hover {
                text-decoration: underline;
            }
        }
        
        .btn-divider {
            width: 1px;
            height: 12px;
            background: #EEEEEE;
            flex-shrink: 0;
        }
    }
}

/* 分页 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 12px 16px;
    border-top: 1px solid #EEEEEE;
    border-radius: 0 0 4px 4px;
    flex-shrink: 0;
    gap: 16px;
    
    .pagination-left {
        .total-text {
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #333333;
        }
    }
    
    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 2px;
            padding: 8px;
            border: 1px solid #EEEEEE;
            border-radius: 4px;
            background: #FFFFFF;
            
            .page-size-text {
                font-family: 'Alibaba PuHuiTi';
                font-size: 24px;
                color: #333333;
            }
            
            .page-size-arrow {
                width: 16px;
                height: 16px;
                position: relative;
                
                &::after {
                    content: '';
                    position: absolute;
                    top: 6px;
                    left: 3px;
                    width: 9.6px;
                    height: 4.8px;
                    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9.6 4.8'%3E%3Cpath d='M1 1l3.8 2.8L8.6 1' stroke='%23999999' stroke-width='1.2' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center;
                    background-size: contain;
                }
            }
        }
        
        .page-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #EEEEEE;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            background: #FFFFFF;
            
            &.disabled {
                opacity: 0.4;
                cursor: not-allowed;
            }
            
            .arrow-left,
            .arrow-right {
                width: 16px;
                height: 16px;
                position: relative;
                
                &::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 4.8px;
                    height: 9.6px;
                    background-size: contain;
                }
            }
            
            .arrow-left::after {
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4.8 9.6'%3E%3Cpath d='M3.8 1L1 4.8l2.8 3.8' stroke='%23999999' stroke-width='1.2' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center;
            }
            
            .arrow-right::after {
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4.8 9.6'%3E%3Cpath d='M1 8.6l2.8-3.8L1 1' stroke='%23999999' stroke-width='1.2' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center;
            }
        }
        
        .page-numbers {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .page-number {
                width: 32px;
                height: 32px;
                border: 1px solid #EEEEEE;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: 'Alibaba PuHuiTi';
                font-size: 24px;
                cursor: pointer;
                background: #FFFFFF;
                color: #333333;
                
                &.active {
                    background: #FFFFFF;
                    color: #287FFF;
                    border-color: #287FFF;
                }
            }
            
            .page-ellipsis {
                padding: 0 5px;
                font-size: 24px;
                color: #999999;
            }
        }
    }
    
    .pagination-right {
        display: flex;
        align-items: center;
        gap: 6px;
        
        .goto-text {
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #333333;
        }
        
        .goto-input {
            width: 40px;
            height: 32px;
            border: 1px solid #EEEEEE;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #FFFFFF;
            
            .goto-input-field {
                width: 100%;
                height: 100%;
                border: none;
                background: transparent;
                text-align: center;
                font-family: 'Alibaba PuHuiTi';
                font-size: 24px;
                color: #333333;
            }
        }
    }
}

/* 日期选择弹窗 */
.date-popover {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    width: 280px;
    background: #FFFFFF;
    border: 1px solid #EEEEEE;
    border-radius: 8px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    padding: 10px 10px 14px;
    
    .popover-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        
        .nav-btn {
            width: 28px;
            height: 28px;
            border-radius: 4px;
            border: 1px solid #EEEEEE;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: #333333;
        }
        
        .ym-text {
            font-size: 16px;
            color: #333333;
        }
    }
    
    .week-row {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 6px;
        margin-bottom: 6px;
        
        .week-cell {
            text-align: center;
            font-size: 12px;
            color: #999999;
        }
    }
    
    .day-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 6px;
        
        .day-cell {
            height: 34px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            color: #333333;
            
            &.other {
                color: #C7C7C7;
            }
            
            &.selected {
                background: #287FFF;
                color: #FFFFFF;
            }
            
            &:hover:not(.selected) {
                background: #F0F0F0;
            }
        }
    }
}
</style>
