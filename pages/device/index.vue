<template>
	<view class="device">
		<view class="device-top">
			<view class="device-top-content">
				<view class="device-top-content-title">
					已连接设备
				</view>
				<view class="device-top-content-box">
					<image class="device-top-content-img" src="/static/device/blu-1.png" mode="widthFix"></image>
					<view class="device-top-content-text">
						<view class="device-top-content-text-value">
							名称：{{!helper.address?'--':helper.address.substring(0, 4) === 'Dbay'?'脑电设备':'BrainWi'}}
						</view>
						<view class="device-top-content-text-value">
							型号：{{helper.connectingDevice&&helper.connectingDevice.name?helper.connectingDevice.name:'--'}}
						</view>
						<view class="device-top-content-text-value">
							电量：{{helper.address?helper.electricity+'%':'--'}}
						</view>
					</view>
				</view>
			</view>
			<view class="device-top-list">
				<view class="device-top-list-title">
					设备列表 <text @click="onClick_scanDevice" class="device-top-list-title-icon iconfont">&#xe631;</text>
				</view>
				<scroll-view v-if="state.bluList[0].address" :scroll-y="true" style="height: 410rpx;">
					<view class="device-top-list-box" v-for="(item,index) in state.bluList" :key="item.address">
						<view class="device-top-list-box-left" v-show="item.address">
							<image class="device-top-list-box-left-img" src="/static/device/blu-icon.png" mode="widthFix"></image>
							<text class="device-top-list-box-left-address">{{item.name}}</text>
							<text class="device-top-list-box-left-status" v-show="helper.address&&helper.address==item.address">已连接</text>
						</view>
						<view class="device-top-list-box-rignt" v-show="item.address" @click="()=>onClick_connectDevice(item)">
							{{helper.address&&helper.address==item.address?'断开' :'连接'}}
						</view>
					</view>
				</scroll-view>
				<view class="device-top-list-none center" v-else>
					暂无搜索到设备
				</view>
			</view>
		</view>
		<view class="device-bottom">
			<view class="device-bottom-title">
				<view class="device-bottom-title-left">
					<image class="device-bottom-title-left-img" src="/static/device/blu-wave.png" mode="widthFix"></image>
					<text>脑电波形</text>
				</view>
				<view class="device-bottom-text center" :class="helper.bluData&&helper.bluData.signal?'device-bottom-text-signal':''" v-if="helper.address">
					脑机设备佩戴：{{helper.bluData&&helper.bluData.signal?'脱落':'良好'}}
				</view>
			</view>
			<EchartsVue :amplitudeDate="amplitudeDate" :address="helper.address" v-if="helper.address" />
			<view class="device-bottom-title-noData center" v-else>
				没有数据显示
			</view>
		</view>
	</view>
</template>

<!-- 原生APP -->
<script setup>
	import {
		reactive,
		onMounted,
		watch,
		ref,
		onUnmounted
	} from "vue";
	import {
		useHelper
	} from "../../stores/helper";
	import EchartsVue from './Echarts.vue';
	import {
		onShow
	} from '@dcloudio/uni-app'
	import {
		hideLoading,
		navigateTo,
		showLoading,
		showToast
	} from '../../common/uniTool';
	import {
		hex2int,
		randomNum
	} from '../../common/method';
	import {
		Blue
	} from '@/utils/bluConfig';
	import {
		BleController
	} from '@/utils/bluType';
	import {
		CTLineNum,
		DbayLineNum,
		changeAddress
	} from "../../common/global";

	import douglasPeucker from '@/utils/douglas_peuker'
	const helper = useHelper(); //设备仓库
	const state = reactive({
		bluList: [{
			address: "",
			name: ''
		}],
	})
	const amplitudeDate = ref({})
	onMounted(() => {
		// #ifdef APP-PLUS
		// 确保 helperBlu 已初始化
		if (!helper.helperBlu) {
			helper.helperBlu = uni.requireNativePlugin('alghelper')
		}
		
		if (helper.isInit) {
			helper.helperBlu && helper.helperBlu.changeListener(onMsg)
		} else {
			helper.helperBlu && helper.helperBlu.init(JSON.stringify({
				accountId: '6440b94d7b2c0faeb29f97a9',
				token: '0e8d7e20-1801-46aa-898b-0bb5ca163f09',
				blRaw: true,
				blDebug: true
			}), onMsg)
		}
		helper.helperBlu && helper.helperBlu.scanDevice()
		if (helper.address) {
			Blue.bleConnectDeviceID = helper.address
			state.bluList = [helper.connectingDevice]
			helper.helperBlu && helper.helperBlu.sendCmd(helper.address, '02')
		}
		ownBluInit()
		// #endif
	})

	onUnmounted(() => {
		console.log('卸载');
		helper.resetBox()
		helper.amplitudeDateArr = {
			AF7Data: [],
			AF8Data: []
		}
		onClick_scanStop()
	})
	watch(() => helper.numBox, (numBox) => {
		if (helper.address.substring(0, 4) === 'Dbay') {
			if (numBox.AF7Data.length >= DbayLineNum) {
				helper.getAmplitudeArr()
			}
		} else {
			if (numBox.AF7Data.length === CTLineNum) {
				helper.getAmplitudeArr()
			}
		}

	}, {
		deep: true
	})
	watch(() => helper.amplitudeDateArr, (amplitudeDateArr) => {
		if (helper.address.substring(0, 4) === 'Dbay') {
			if (amplitudeDateArr.AF7Data.length >= DbayLineNum) {
				amplitudeDate.value = {
					AF7Data: douglasPeucker(amplitudeDateArr.AF7Data.map((item, index) => [index, item + 25]), 2),
					AF8Data: douglasPeucker(amplitudeDateArr.AF8Data.map((item, index) => [index, item + 75]), 2),
				}
				helper.amplitudeDateArr = {
					AF7Data: [],
					AF8Data: []
				}
			}
		} else {
			if (amplitudeDateArr.AF7Data.length === CTLineNum) {
				amplitudeDate.value = {
					AF7Data: douglasPeucker(amplitudeDateArr.AF7Data.map((item, index) => [index, item + 25]), 4),
					AF8Data: douglasPeucker(amplitudeDateArr.AF8Data.map((item, index) => [index, item + 75]), 4),
				}
				helper.amplitudeDateArr = {
					AF7Data: [],
					AF8Data: []
				}
			}
		}

	}, {
		deep: true
	})
	const ownBluInit = () => {
		helper.resetBox()
		helper.amplitudeDateArr = {
			AF7Data: [],
			AF8Data: []
		}
		Blue.start()
		BleController.addDeviceAcceptListListen(state => {
			// console.log('数据接受中', state);
			let value = JSON.parse(state)
			helper.bluData = value
			helper.addAmplitudeDate(value)
			if (value.ele !== helper.electricity) {
				helper.electricity = value.ele
			}
		})
		BleController.addConnectStateListen(state => {
			// console.log(state, '-------------');
			if (state.code === 200 && state.deviceInfo) {
				connectSuccess(state.deviceInfo.address, state.deviceInfo.name) //连接成功
				Blue.isInit = true
			} else if (state.code === -1) {
				uni.showModal({
					title: state.label,
					showCancel: false,
				});
			} else if (state.code === -2) {
				showToast(state.label)
			} else if (state.code === 500) {
				changeAddress('')
				helper.address = ''
				helper.connectingDevice = null
				Blue.bleConnectDeviceID = null
				showToast(state.label)
			}
		})
		BleController.addDeviceListListen(res => {
			const bList = [{
				"name": res.name,
				"address": res.deviceId,
			}]
			if (!state.bluList[0].address) {
				state.bluList = bList
			}
			state.bluList = [...filterList(state.bluList, bList)]
		})
	}
	const onClick_scanDevice = () => {
		// console.log('扫描');
		helper.helperBlu && helper.helperBlu.scanStop()
		helper.helperBlu && helper.helperBlu.scanDevice()
		Blue.start()
	}
	const onClick_scanStop = () => {
		// console.log('停止扫描');
		helper.helperBlu && helper.helperBlu.scanStop()
		Blue.stopBluetoothDevicesDiscovery()
	}

	//去掉重复数组
	const filterList = (arr1, arr2) => {
		let values = [...arr1, ...arr2]
		let map = new Map()
		for (let item of values) {
			if (!map.has(item.address)) {
				map.set(item.address, item)
			}
		}
		return map.values()
	}

	//连接成功
	const connectSuccess = (value, name) => {
		helper.address = value
		changeAddress(name)
		helper.connectingDevice = {
			"name": name,
			"address": value,
		}
		onClick_scanStop()
		hideLoading()
		showToast('设备连接成功，请开始你的测评吧~')
	}

	const onMsg = (json) => {
		helper.isInit = true
		const ret = JSON.parse(json)
		let type = ret.type
		if (ret.type == 0 && ret.state == 0) {
			onClick_scanDevice()
		} else if (type == 2) {
			let bList = helper.helperBlu ? JSON.parse(helper.helperBlu.getDeviceList()) : []
			if (!state.bluList[0].address) {
				state.bluList = bList
			}
			state.bluList = [...filterList(state.bluList, bList)]
		} else if (ret.state == 2) {
			if (type == 3) {
				setTimeout(() => {
					helper.helperBlu && helper.helperBlu.sendCmd(ret.address, '02')
				}, 500)
				connectSuccess(ret.address, ret.address) //连接成功
			}
		} else if (type == 4) {
			if (ret.type == 4) {
				showToast(`连接失败，请确定设备${ret.address}是否打开`)
			}
		} else if (type == 5 || type == 1) {
			if (type == 5) {
				changeAddress('')
				helper.address = ''
				helper.connectingDevice = null
				Blue.bleConnectDeviceID = null
				showToast('蓝牙设备已断开')
			}
			helper.bluData = ret
		} else if (type == 7) {
			helper.addAmplitudeDate(ret)
		} else if (type == 6) {
			if (ret.hex.indexOf('82') != -1) {
				helper.electricity = hex2int(ret.hex.slice(2, 4).toString())
			}
		}
	}
	//断连
	const disconnectDevice = (device) => {
		if (device.name.substring(0, 4) === 'CT10') { //自己设备
			Blue.closeBLEConnection(device.address)
		} else {
			helper.helperBlu && helper.helperBlu.disconnectDevice(device.address)
		}
		Blue.bleConnectDeviceID = null
	}
	//连接
	const connectDevice = (device) => {
		// console.log(device, 'connectDevice');
		if (device.name.substring(0, 4) === 'CT10') { //自己设备
			Blue.createBLEConnection(device)
		} else {
			helper.helperBlu && helper.helperBlu.connectDevice(device.address)
		}
	}
	const onClick_connectDevice = (device) => {
		if (!state.bluList[0].address) {
			showToast('没有搜索到蓝牙设备哦~')
			return
		}
		helper.resetBox()
		helper.bluData = null
		if (helper.address === device.address) {
			disconnectDevice(device) //断连
			changeAddress('')
			helper.address = ''
			helper.connectingDevice = null
			showToast('设备蓝牙已断开')
			return
		}
		if (helper.address && helper.address !== device.address) {
			disconnectDevice(helper.connectingDevice) //断连
		}
		changeAddress('')
		helper.address = ''
		helper.connectingDevice = null
		showLoading('设备连接中...')
		connectDevice(device) //连接
	}
</script>

<style lang="scss">
	.device {
		width: 100vw;
		height: 100vh;
		flex: 1;
		background: #EEEEEE;
		display: flex;
		flex-direction: column;

		&-bottom {
			background: #FFFFFF;
			display: flex;
			flex-direction: column;
			margin-top: 8rpx;
			width: 100%;
			flex: 1;

			&-text {
				background: #F7FAFF;
				border-radius: 16rpx;
				padding: 16rpx 60rpx;
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 45rpx;
				color: #287FFF;

				&-signal {
					background: #FFF7F7;
					color: #FF4747;
				}
			}

			&-title {
				width: 100%;
				display: flex;
				align-items: center;
				font-family: PingFangSC, PingFang SC;
				font-weight: bold;
				font-size: 60rpx;
				color: #111111;
				padding: 20rpx 60rpx;

				&-noData {
					height: 300rpx;
					font-family: PingFangSC, PingFang SC;
					font-weight: bold;
					font-size: 38rpx;
					color: #999999;
				}

				&-left {
					display: flex;
					align-items: center;
					flex: 1;

					&-img {
						width: 74rpx;
						height: 74rpx;
						margin-right: 16rpx;
					}
				}

				&-full {
					display: flex;
					align-items: center;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
					background: #2C72DA;
					border-radius: 8rpx;
					padding: 12rpx 16rpx;

					&-icon {
						margin-left: 10rpx;
					}
				}

				&-max {
					display: flex;
					align-items: center;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					padding: 12rpx 16rpx;
					color: #111111;
					background: #F6F6F6;
					border: 1rpx solid #E3E3E3;
					border-radius: 8rpx;
					margin-left: 16rpx;
				}
			}


		}

		&-top {
			display: flex;
			background: #FFFFFF;
			align-items: flex-start;
			width: 100%;
			padding: 14rpx 40rpx;

			&-nocontent {
				display: flex;
				flex-direction: column;
				align-items: center;
				height: 300rpx;
				justify-content: center;

				&-img {
					width: 204rpx;
					margin-bottom: 32rpx;
				}

				&-text {
					font-size: 20rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #999999;
				}
			}

			&-title {
				font-size: 32rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #111111;
			}

			&-content {
				height: 100%;
				width: 56%;
				border-radius: 8rpx;
				display: flex;
				flex-direction: column;
				justify-content: center;

				&-title {
					font-family: PingFangSC, PingFang SC;
					font-weight: bold;
					font-size: 60rpx;
					color: #111111;
					margin-bottom: 20rpx;
				}

				&-box {
					padding: 46rpx 44rpx;
					background: #F6F6F6;
					display: flex;
					align-items: center;
				}

				&-img {
					width: 562rpx;
				}

				&-text {
					height: 100%;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					font-size: 45rpx;
					color: #111111;
					line-height: 45rpx;
					margin-left: 38rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-around;
				}
			}

			&-list {
				margin-left: 28rpx;
				flex: 1;
				height: 100%;
				display: flex;
				flex-direction: column;

				&-none {
					font-family: PingFangSC, PingFang SC;
					font-weight: bold;
					font-size: 38rpx;
					color: #999999;
					width: 100%;
					height: 100%;
				}

				&-title {
					font-family: PingFangSC, PingFang SC;
					font-weight: bold;
					font-size: 60rpx;
					color: #111111;
					margin-bottom: 20rpx;

					&-icon {
						color: #287FFF;
						margin-left: 10rpx;
					}
				}

				&-box {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 30rpx;

					&-left {
						display: flex;
						align-items: center;
						flex: 1;

						&-img {
							width: 104rpx;
							height: 104rpx;
						}

						&-address {
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 45rpx;
							color: #111111;
							margin-left: 24rpx;
							margin-right: 14rpx;
						}

						&-status {
							border-radius: 4rpx;
							border: 2rpx solid #111111;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 34rpx;
							color: #111111;
							padding: 6rpx 16rpx;
						}
					}

					&-rignt {
						background: #F6F6F6;
						border-radius: 80rpx;
						font-family: PingFangSC, PingFang SC;
						font-weight: 400;
						font-size: 45rpx;
						color: #287FFF;
						padding: 16rpx 60rpx;
					}
				}
			}
		}
	}
</style>