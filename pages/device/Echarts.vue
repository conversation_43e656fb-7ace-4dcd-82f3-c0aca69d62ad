<template>
	<view class="content">
		<!-- #ifdef APP-PLUS || H5 -->
		<view @click="echarts.onClick" :name="address" :change:name="echarts.updateName" :prop="amplitudeDate" :change:prop="echarts.updateEcharts" id="echarts" class="echarts"></view>
		<image class="contentImg" src="../../static/device/wave-title.png" mode="heightFix"></image>
		<!-- #endif -->
		<!-- #ifndef APP-PLUS || H5 -->
		<view>非 APP、H5 环境不支持</view>
		<!-- #endif -->
	</view>
</template>

<script>
	export default {
		name: 'Echarts',
		props: {
			amplitudeDate: {
				required: true
			},
			address: {
				required: true
			}
		},
		created() {
			// props 会暴露到 `this` 上
			// console.log("this.option1: " + JSON.stringify(this.amplitudeDate));
		},
		methods: {
			onViewClick(options) {
				console.log(options)
			}
		}
	}
</script>

<script module="echarts" lang="renderjs">
	let myChart
	let data1 = new Array(100).fill(25)
	let data2 = new Array(100).fill(50)
	export default {
		data() {
			return {
				address: '',
				option: {
					animation: true,
					animationDuration: 5000,
					grid: {
						left: '15%',
						right: '0%',
						bottom: '0%'
					},
					legend: {
						data: ['AF8Data', 'AF7Data']
					},
					xAxis: {
						name: 'x',
						minInterval: 1,
						minorSplitLine: {
							show: false
						},
						axisLabel: {
							show: false
						},
						axisLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						splitLine: {
							show: false,
						}
					},
					yAxis: {
						min: 0,
						max: 100,
						minorTick: {
							show: false
						},
						minorSplitLine: {
							show: false
						},
						axisLabel: {
							show: false
						},
						axisLine: {
							show: false
						},
						axisTick: {
							show: false
						},
						splitLine: {
							show: false
						}
					},
					series: [{
						name: 'AF7Data',
						type: 'line',
						showSymbol: false,
						clip: false,
						data: data1,
						smooth: true,
						lineStyle: {
							width: 1
						},
						markLine: {
							silent: true,
							symbol: "none",
							label: {
								show: false
							},
							animation: false,
							lineStyle: {
								color: '#333'
							},
							data: [{
									xAxis: 250,
								},
								{
									xAxis: 500
								},
								{
									xAxis: 750
								},
								{
									xAxis: 1000
								},
								{
									xAxis: 1250
								},
								{
									xAxis: 1500
								}
							]
						}
					}, {
						name: 'AF8Data',
						type: 'line',
						showSymbol: false,
						clip: false,
						data: data2,
						smooth: true,
						lineStyle: {
							width: 1
						}
					}]
				}
			}
		},
		mounted() {
			if (typeof window.echarts === 'function') {
				this.initEcharts()
			} else {
				// 动态引入较大类库避免影响页面展示
				const script = document.createElement('script')
				// view 层的页面运行在 www 根目录，其相对路径相对于 www 计算
				script.src = 'static/echarts.js'
				script.onload = this.initEcharts.bind(this)
				document.head.appendChild(script)
			}
		},
		methods: {
			initEcharts() {
				myChart = echarts.init(document.getElementById('echarts'))
				// 观测更新的数据在 view 层可以直接访问到
				myChart.setOption(this.option);
			},
			updateName(newValue, oldValue, ownerInstance, instance) {
				this.address = newValue
				console.log(newValue);
			},
			updateEcharts(newValue, oldValue, ownerInstance, instance) {
				myChart && myChart.clear()
				// 监听 service 层数据变更
				if (this.address.substring(0, 4) !== 'Dbay') {
					this.option.series[0].markLine.data = [{
							xAxis: 500,
						},
						{
							xAxis: 1000
						},
						{
							xAxis: 1500
						},
						{
							xAxis: 2000
						},
						{
							xAxis: 2500
						},
						{
							xAxis: 3000
						},
						{
							xAxis: 3500
						}
					]
				}
				if (Object.keys(newValue).length > 0) {
					data1 = newValue.AF7Data
					data2 = newValue.AF8Data
					this.option.series[0].data = data1
					this.option.series[1].data = data2
					myChart && myChart.setOption(this.option)
				}
			},
			onClick(event, ownerInstance) {
				// 调用 service 层的方法
				ownerInstance.callMethod('onViewClick', {
					test: 'test'
				})
			}
		}
	}
</script>

<style>
	.content {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 100%;
		position: relative;
	}

	.contentImg {
		height: 258rpx;
		position: absolute;
		left: 60rpx;
		top: 58%;
		transform: translateY(-50%);
	}

	.echarts {
		width: 100%;
		height: 100%;
	}
</style>