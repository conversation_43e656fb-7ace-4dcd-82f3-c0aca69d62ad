<template>
	<view class="search" @click="state.showPop=false">
		<!-- 		<uni-search-bar class="search-input" @confirm="search" placeholder="" @cancel="cancel" @clear="clear" :focus="true" v-model="state.formData.searchValue">
		</uni-search-bar> -->
		<uv-form :model="state.formData" ref="uvFormRef" class="search-form" :rules="state.rules">
			<view class="search-form-box">
				<uv-form-item label="姓名或者编号:" prop="param" labelWidth="104">
					<uv-input v-model="state.formData.param" customStyle="width:900rpx" class="search-form-search" prefixIcon="search" :prefixIconStyle="prefixIconStyle"
						placeholder="请输入完整的姓名或者编号查询" />
				</uv-form-item>
				<view class="search-form-box-test">
					<view class="search-form-box-test-time">
						评估类型：
					</view>
					<uv-radio-group v-model="state.formData.sourceType">
						<uv-radio :customStyle="{marginRight: '16rpx'}" name="SNAP-IV" label="SNAP-IV"></uv-radio>
						<uv-radio name="PPVT" label="PPVT" :customStyle="{marginRight: '16rpx'}"></uv-radio>
						<uv-radio name="CPT-STD" label="IVA-CPT(STD)" :customStyle="{marginRight: '16rpx'}"></uv-radio>
						<uv-radio name="CPT-MINI" label="IVA-CPT(MINI)" v-if="userStore.userInfo.cptMiniOnOff==='1'"></uv-radio>
					</uv-radio-group>
				</view>

				<!-- 	<view class="search-form-add center" @click="goHome">
					<text class="iconfont" style="font-size: 60rpx;">&#xe612;</text>
					新建评估
				</view>
				<view class="search-form-down center" @click="openOther">
					筛选展开 <uv-icon size="28" :name="state.showOther?'arrow-up':'arrow-down'" color="#41A8DE"></uv-icon>
				</view> -->
			</view>
			<view class="search-form-box" v-if="state.showOther">
				<view class="search-form-box-test">
					<!-- <view class="search-form-box-test-time">
						
					</view> -->
					<uv-form-item label="测试时间：" prop="beginDate" labelWidth="84">
						<uv-input v-model="state.data.beginDate" placeholder="测试开始时间" @click="()=>onclick(1)" :readonly="true" suffixIcon="calendar" suffixIconStyle="color: #000000"></uv-input>
					</uv-form-item>
					<text class="search-form-box-test-text">至</text>
					<uv-form-item prop="endDate" labelWidth="84">
						<uv-input v-model="state.data.endDate" placeholder="测试结束时间" @click="()=>onclick(2)" :readonly="true" suffixIcon="calendar" suffixIconStyle="color: #000000"></uv-input>
					</uv-form-item>
				</view>
				<view class="search-form-box-test">
					<view class="search-form-box-test-time">
						年龄/年龄段：
					</view>
					<uv-input placeholder="请输入开始年龄" v-model="state.data.ageBegin" :readonly="true" suffixIcon="arrow-down-fill" @click="()=>onclick(3)" suffixIconStyle="color: #000000"></uv-input>
					<text class="search-form-box-test-text">至</text>
					<uv-input placeholder="请输入结束年龄" v-model="state.data.ageEnd" :readonly="true" suffixIcon="arrow-down-fill" @click="()=>onclick(4)" suffixIconStyle="color: #000000"></uv-input>
				</view>
				<view class="search-form-box-test">
					<!-- 	<view class="search-form-box-test-time">
						
					</view> -->
					<uv-form-item label="测试分数：" prop="minTotalScore" labelWidth="84">
						<uv-input placeholder="请输入最小分" customStyle="width:440rpx" maxlength="3" type="number" v-model="state.formData.minTotalScore"></uv-input>
					</uv-form-item>
					<text class="search-form-box-test-text">至</text>
					<uv-form-item prop="maxTotalScore" labelWidth="84">
						<uv-input placeholder="请输入最大分" customStyle="width:440rpx" maxlength="3" v-model="state.formData.maxTotalScore" type="number"></uv-input>
					</uv-form-item>
				</view>
				<view class="search-form-box-test">
					<view class="search-form-box-test-time">
						性 别：
					</view>
					<uv-radio-group v-model="state.formData.sex">
						<uv-radio :customStyle="{marginRight: '30px'}" :name="1" label="男"></uv-radio>
						<uv-radio :name="2" label="女"></uv-radio>
					</uv-radio-group>
					<view class="search-form-conform center" @click="submit">
						搜索
					</view>
					<view class="search-form-reset center" @click="reset">
						重置
					</view>
				</view>
			</view>
			<uv-picker ref="ageBeginPicker" :columns="state.columnData" @confirm="(e)=>confirmAge(e,1)" @change="change"></uv-picker>
			<uv-picker ref="ageEndPicker" :columns="state.columnData" @confirm="(e)=>confirmAge(e,2)" @change="change"></uv-picker>
			<uv-picker ref="agePicker" :columns="state.sexColumns" @confirm="(e)=>confirmAge(e,3)" @change="change"></uv-picker>
			<uv-datetime-picker ref="startTimePicker" @confirm="confirmStart" mode="date" v-model="state.formData.beginDate">
			</uv-datetime-picker>
			<uv-datetime-picker ref="endTimePicker" @confirm="confirmEnd" mode="date" v-model="state.formData.endDate">
			</uv-datetime-picker>
		</uv-form>
		<scroll-view class="search-value" v-if="state.list.length" :scroll-y="true" lower-threshold="100" :show-scrollbar="true" @scrolltolower="scrolltolower">
			<view class="search-value-list">
				<view class="search-value-list-title">
					<view class="search-value-list-title-one center" style="width: 100rpx;">
						序号
					</view>
					<view class="search-value-list-title-one center" style="width: 400rpx;">
						编号
					</view>
					<view class="search-value-list-title-one center" style="width: 180rpx;">
						姓名
					</view>
					<view class="search-value-list-title-one center" style="width: 150rpx;">
						性别
					</view>
					<view class="search-value-list-title-one center">
						年龄
					</view>
					<view class="search-value-list-title-one center" style="width: 200rpx;">
						评估类型
					</view>
					<view class="search-value-list-title-one center" style="width: 480rpx;">
						测试时间
					</view>
					<view class="search-value-list-title-one center" style="width: 130rpx;">
						分数
					</view>
					<view class="search-value-list-title-one center" style="width: 380rpx;">
						操作
					</view>
				</view>
				<view class="search-value-list-item">
					<view v-for="(item,index) in state.list" class="search-value-list-item-box" :key="index">
						<view class="search-value-list-item-box-value center" style="width: 100rpx;">
							{{item.id}}
						</view>
						<view class="search-value-list-item-box-value center" style="width: 400rpx;">
							{{item.serNum}}
						</view>
						<view class="search-value-list-item-box-value center" style="width: 180rpx;">
							{{item.userName}}
						</view>
						<view class="search-value-list-item-box-value center" style="width: 150rpx;">
							{{!item.sex?'无':item.sex==1?'男':'女'}}
						</view>
						<view class="search-value-list-item-box-value center">
							{{item.age}}
						</view>
						<view class="search-value-list-item-box-value center" style="width: 200rpx;">
							{{item.sourceType}}
						</view>
						<view class="search-value-list-item-box-value center" style="width: 480rpx;">
							{{item.createDate}}
						</view>
						<view class="search-value-list-item-box-value center" style="width: 130rpx;">
							{{item.totalScore}}
						</view>
						<view class="search-value-list-item-box-btn center" style="width: 380rpx;">
							<view class="search-value-list-item-box-btn-item center" @click.stop="()=>open(item,index)">
								查看报告
								<view class="search-value-list-item-box-btn-item-box" v-if="state.indexValue===index&&state.showPop">
									<view v-for="(item,index) in state.downTypes" :class="!index?'search-value-list-item-box-btn-item-box-item':''" :key="index"
										@click.stop="openFile(state.fileList[item])">
										{{item}}
									</view>
								</view>
							</view>
							<view class="search-value-list-item-box-btn-item center" @click="()=>again(item.outpatientId,item.sourceType,item.userNamem)">
								再次评估
							</view>
						</view>
					</view>
				</view>
			</view>
			<uni-load-more iconType="circle" :status="state.status" />
		</scroll-view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		showToast,
		getStorageSync,
		hideLoading,
		showLoading,
		navigateTo
	} from "../../common/uniTool";
	import {
		getQryFreeEvaInfoList,
		qryFreeEvaResultPDF,
		qryPPVTUserInfo
	} from "../../service";
	import {
		getTime
	} from "../../common/method";
	import {
		useUserStore
	} from '../../stores/user';
	import {
		exportIvacptStdPdf,
		exportIvacptMiniPdf
	} from "../../service/ivacpt";
	import {
		reportUrl
	} from "../../common/global";
	const userStore = useUserStore()
	const prefixIconStyle = "font-size: 32px;color: #3481FD;border-right:4rpx solid #999999;width:80rpx;margin-right:10rpx;font-weight:bold"
	const agePicker = ref(null)
	const ageBeginPicker = ref(null)
	const ageEndPicker = ref(null)
	const endTimePicker = ref(null)
	const uvFormRef = ref(null)
	const startTimePicker = ref(null)
	const state = reactive({
		downTypes: [],
		status: 'more', //下拉加载状态
		searchValue: '',
		fileList: null,
		showPop: false,
		indexValue: -1,
		pageIndex: 1,
		list: [],
		formData: {
			param: null,
			beginDate: null,
			endDate: null,
			ageEnd: null,
			ageBegin: null,
			maxTotalScore: null,
			minTotalScore: null,
			sex: null,
			sourceType: null
		},
		data: {
			beginDate: '',
			endDate: '',
			ageEnd: '',
			ageBegin: '',
			sex: ''
		},
		showOther: true,
		columns: [
			[3, 4, 5, 6, 7, 8],
			[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
		],
		rules: {
			'endDate': {
				required: false,
				message: '结束时间需要大于起始时间',
				validator: (rule, value, valcallback) => {
					if (state.formData.beginDate <= value) {
						return true
					} else {
						return false
					}
					// 上面有说，返回true表校验通过，返回false表示不通过
				},
			},
			'minTotalScore': {
				required: false,
				message: '请正确分数',
				validator: (rule, value, valcallback) => {
					if (value >= 0 && value <= 160) {
						return true
					} else {
						return false
					}
					// 上面有说，返回true表校验通过，返回false表示不通过
				},
			},
			'maxTotalScore': {
				required: false,
				message: '请正确分数',
				validator: (rule, value, valcallback) => {
					if (value >= 0 && value <= 160) {
						return true
					} else {
						return false
					}
					// 上面有说，返回true表校验通过，返回false表示不通过
				},
			},
		},
		sexColumns: [
			['女', '男']
		],
		columnData: [
			['3岁', '4岁', '5岁', '6岁', '7岁', '8岁'],
			['1个月', '2个月', '3个月', '4个月', '5个月', '6个月', '7个月', '8个月', '9个月', '10个月', '11个月', '12个月']
		],
	})

	const change = () => {
		console.log('change');
	}
	const submit = () => {
		uvFormRef.value.validate().then(res => {
			state.formData.maxTotalScore = state.formData.maxTotalScore ? Number(state.formData.maxTotalScore) : null
			state.formData.minTotalScore = state.formData.minTotalScore ? Number(state.formData.minTotalScore) : null
			state.pageIndex = 1
			state.clickIndex = 0
			state.list = []
			getList()
		}).catch(errors => {
			uni.showToast({
				icon: 'error',
				title: '搜索失败'
			})
		})
	}
	const openFile = (value) => {
		uni.downloadFile({
			url: value,
			success: function (res) {
				let filePath = res.tempFilePath;
				console.log(filePath);
				showLoading('生成报告中....')
				state.showPop = false
				uni.openDocument({
					filePath: filePath,
					showMenu: true,
					success: function (res) {
						hideLoading()
						console.log('打开文档成功');
					},
					fail() {
						showToast('打开失败')
						hideLoading()
					}
				});
			}
		});
	}
	const reset = () => {
		uvFormRef.value.resetFields()
		uvFormRef.value.clearValidate()
		state.formData = {
			param: null,
			beginDate: null,
			endDate: null,
			ageEnd: null,
			ageBegin: null,
			maxTotalScore: null,
			minTotalScore: null,
			sex: null,
			sourceType: null
		}
		state.data = {
			beginDate: '',
			endDate: '',
			ageEnd: '',
			ageBegin: '',
			sex: ''
		}
	}
	// const goHome = () => {
	// 	navigateTo(`/pages/index/index?type=${props.type}`)
	// }
	const onclick = (type) => {
		switch (type) {
			case 1:
				startTimePicker.value.open();
				break;
			case 2:
				endTimePicker.value.open();
				break;
			case 3:
				ageBeginPicker.value.open();
				break;
			case 4:
				ageEndPicker.value.open();
				break;
			case 5:
				agePicker.value.open();
				break;
			default:
				break;
		}
	}
	const confirmAge = (e, type) => {
		console.log(e);
		if (type == 1) {
			state.data.ageBegin = e.value.join("")
			state.formData.ageBegin = `${state.columns[0][e.indexs['0']]},${state.columns[1][e.indexs['1']]}`
		} else if (type == 2) {
			state.data.ageEnd = e.value.join("")
			state.formData.ageEnd = `${state.columns[0][e.indexs['0']]},${state.columns[1][e.indexs['1']]}`
		} else {
			state.data.sex = e.value.join("")
			state.formData.sex = e.value.join("") == '女' ? 2 : 1
		}
	}
	const confirmStart = (e) => {
		state.data.beginDate = getTime(e.value)
		state.formData.beginDate = e.value
	}
	const again = (outpatientId, sourceType, userName) => {
		userStore.outpatientId = outpatientId
		userStore.userName = userName
		if (sourceType === 'PPVT') {
			qryPPVTUserInfo({
				traineeId: outpatientId
			}).then(res => {
				if (res.data.canPPvt) {
					navigateTo('/pages/introduce/index')
				} else {
					showToast('不在本测评所对应的年龄范围内~')
				}
			})
		} else if (sourceType == 'SNAP-IV') {
			navigateTo('/pages/scale/introduce')
		} else if (sourceType == 'CPT-MINI') {
			navigateTo('/pages/ivacpt/introduce?type=mini')
		} else {
			navigateTo('/pages/ivacpt/introduce')
		}
	}
	const confirmEnd = (e) => {
		state.data.endDate = getTime(e.value)
		state.formData.endDate = e.value
	}
	onShow(() => {
		state.pageIndex = 1
		state.list = []
		getList()
		
		//#ifdef APP-PLUS
		// 确保页面为全屏模式
		plus.navigator.setFullscreen(true);
		//#endif
	})
	const getList = async () => {
		state.status = 'loading'
		let res = await getQryFreeEvaInfoList({
			param: state.formData,
			pageSize: 10,
			pageIndex: state.pageIndex
		})
		state.list.push(...res.data)
		state.total = res.total
		if (res.total <= state.list.length) {
			state.status = "no-more"
		} else {
			state.status = "more"
		}
	}
	const openOther = () => {
		state.showOther = !state.showOther
	}

	const clear = () => {
		state.list = []
	}
	const cancel = () => {
		console.log('取消');
		state.list = []
	}
	const scrolltolower = () => {
		if (state.total <= state.list.length) {
			state.status = "no-more"
			return
		}
		state.pageIndex++
		getList()
	}
	const open = async (item, index) => {
		if (item.sourceType === 'SNAP-IV') {
			navigateTo(`/pages/report/index?round=${item.round}&userName=${item.userName}&outpatientId=${item.outpatientId}`)
		} else {
			if (index === state.indexValue) {
				state.showPop = false
				state.fileList = null
				state.indexValue = -1
				return
			}
			let res
			if (item.sourceType === 'CPT-STD') {
				res = await exportIvacptStdPdf({
					evaluatId: item.evaluatId,
				})
				if (!res.data.pdf) {
					navigateTo(`/pages/webview/index?url=${reportUrl}#/ivacpt/${getStorageSync('token')}/${item.outpatientId}/${item.evaluatId}/PPVT/13`)
					return
				}
			} else if (item.sourceType === 'CPT-MINI') {
				res = await exportIvacptMiniPdf({
					evaluatId: item.evaluatId,
				})

			} else {
				res = await qryFreeEvaResultPDF({
					round: item.round,
					outpatientId: item.outpatientId,
					userName: item.userName
				})
			}
			let list = Object.keys(res.data).filter(item => item != "outpatientId")
			state.downTypes = list
			state.showPop = true
			state.fileList = res.data
			state.indexValue = index
		}
	}
</script>

<style lang="scss">
	.search {
		width: 100vw;
		height: 100vh;
		min-height: 100vh;
		display: flex;
		flex-direction: column;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1000;
		background: #FFFFFF;

		&-form {
			width: 100vw;
			background: #FFFFFF;
			padding: 0 50rpx 10rpx 50rpx;

			&-add {
				padding: 0 16rpx;
				border-radius: 13rpx;
				border: 2rpx solid #999999;
				height: 88rpx;
				background: #FFFFFF;
				border-radius: 13rpx;
				font-size: 36rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #111111;
				margin-right: 25rpx;
			}

			&-box {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;

				&-test {
					display: flex;
					align-items: center;
					width: 45vw;

					&-text {
						padding: 0 12rpx;
					}
				}
			}

			&-search {
				width: 30vw;
			}

			&-conform {
				width: 240rpx;
				height: 75rpx;
				background: #287FFF;
				border-radius: 15rpx;
				font-size: 50rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
				margin-right: 30rpx;
			}

			&-reset {
				width: 240rpx;
				height: 75rpx;
				border-radius: 15rpx;
				border: 2rpx solid #999999;
				font-size: 50rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #111111;
			}

			&-down {
				font-size: 50rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #41A8DE;
			}
		}

		&-value {
			background: #F6F6F6;
			height: 68vh;
			padding: 20rpx;

			&-list {
				background: #FFFFFF;
				border-radius: 3rpx;
				padding: 20rpx;

				&-item {
					display: flex;
					justify-content: space-between;
					align-items: center;
					width: 100%;
					font-size: 40rpx;
					flex-direction: column;

					&-box {
						display: flex;
						justify-content: space-between;
						align-items: center;
						width: 100%;
						border-bottom: 4rpx solid #EEEEEE;

						&-value {
							width: 250rpx;
							height: 100rpx;
						}

						&-btn {
							width: 200rpx;
							height: 70rpx;
							font-size: 35rpx;
							font-family: SourceHanSansCN-Regular, SourceHanSansCN;
							font-weight: 400;
							color: #41A8DE;
							display: flex;
							align-items: center;
							justify-content: space-around;


							&-item {
								width: 169rpx;
								height: 75rpx;
								border-radius: 13rpx;
								border: 3rpx solid #41A8DE;
								position: relative;

								&-box {
									width: 169rpx;
									position: absolute;
									top: 76rpx;
									padding: 10rpx 10rpx;
									left: 0;
									background: #FFFFFF;
									box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.25);
									border-radius: 8px;
									display: flex;
									align-items: center;
									flex-direction: column;
									font-size: 18px;
									font-family: PingFangSC, PingFang SC;
									font-weight: 400;
									color: #111111;
									z-index: 999;

									&-item {
										border-bottom: 1px solid #E3E3E3;
										margin-bottom: 8rpx;
										height: 60rpx;
									}
								}
							}
						}
					}

					&-box:last-child {
						border: none;
					}
				}

				&-title {
					display: flex;
					align-items: center;
					justify-content: space-between;

					&-one {
						width: 250rpx;
						height: 80rpx;
						background: #E9F7FF;
						border-radius: 3rpx;
						font-size: 40rpx;
						font-family: SourceHanSansCN-Regular, SourceHanSansCN;
						font-weight: 400;
						color: #111111;
					}
				}
			}
		}
	}
</style>