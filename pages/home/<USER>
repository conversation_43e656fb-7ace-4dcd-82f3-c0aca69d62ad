<template>
	<view class="home" v-if="state.show">
		<view class="home-project">
			<view class="home-project-box center" @click="goiva" :style="{width:userStore.userInfo.cptMiniOnOff==='0'?'640rpx':''}">
				<view class="home-project-box-icon center">
					<image src="/static/iva-cpt.png" mode="widthFix" class="home-project-box-icon-img"></image>
				</view>
				<view class="home-project-box-text">
					IVA-CPT(标准版)
				</view>
				<view class="home-project-box-tips">
					（适用于6岁及以上）
				</view>
			</view>
			<view class="home-project-box center" @click="goivaMini" v-if="userStore.userInfo.cptMiniOnOff==='1'">
				<view class="home-project-box-icon center">
					<image src="/static/ivacptmin.png" mode="widthFix" class="home-project-box-icon-img"></image>
				</view>
				<view class="home-project-box-text">
					IVA-CPT(MINI)
				</view>
				<view class="home-project-box-tips">
					（适用于6岁及以上）
				</view>
			</view>
			<view class="home-project-box center" @click="goRep" :style="{width:userStore.userInfo.cptMiniOnOff==='0'?'640rpx':''}">
				<view class="home-project-box-icon center" style="color: #7DDEF8;">
					<text class="iconfont">&#xe675;</text>
				</view>
				<view class="home-project-box-text">
					PPVT
				</view>
				<view class="home-project-box-tips">
					（适用于3岁3个月到8岁5个月)
				</view>
			</view>
			<view class="home-project-box center" @click="goSca" :style="{width:userStore.userInfo.cptMiniOnOff==='0'?'640rpx':''}">
				<view class="home-project-box-icon center">
					<text class="iconfont">&#xe621;</text>
				</view>
				<view class="home-project-box-text">
					量表评估
				</view>
				<view class="home-project-box-tips" style="color:#FFFFFF ;">
					1
				</view>
			</view>

		</view>
		<uni-popup ref="popup" type="center">
			<view class="home-pop">
				<view class="home-pop-title">
					提示
				</view>
				<view class="home-pop-text">
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 本测评适用于<text style="color:#FF0000 ;"> 6岁及以上儿童</text>。当前年龄过小，可能导致测评结果不准确。请确认是否继续。
				</view>
				<view class="home-pop-btn">
					<view class="home-pop-btn-left center" @click="close">
						取消
					</view>
					<view class="home-pop-btn-right center" @click="go">
						仍要继续
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		navigateTo,
		showToast
	} from '../../common/uniTool';
	import {
		onMounted,
		reactive,
		ref
	} from "vue";
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		useUserStore
	} from '../../stores/user';
	import {
		qryPPVTUserInfo
	} from '../../service';
	const userStore = useUserStore()
	const popup = ref(null)
	const state = reactive({
		show: true,
		mini: false
	})
	onShow(() => {
		// #ifdef APP-PLUS
		uni.showLoading({
			title: "正在进入浙大儿院心理评估系统..."
		})
		setTimeout(() => {
			plus.screen.lockOrientation('landscape-primary');
			uni.hideLoading();
			state.show = true
		}, 500)
		//#endif
	})
	const goiva = () => {
		if (userStore.year < 6) {
			popup.value.open('center')
		} else {
			go()
		}
	}
	const goivaMini = () => {
		state.mini = true
		if (userStore.year < 6) {
			popup.value.open('center')
		} else {
			navigateTo('/pages/ivacpt/introduce?type=mini')
		}
	}
	const go = () => {
		close()
		if (state.mini) {
			navigateTo('/pages/ivacpt/introduce?type=mini')
		} else {
			navigateTo('/pages/ivacpt/introduce')
		}
	}
	const close = () => {
		popup.value.close()
	}
	const goSca = () => {
		navigateTo('/pages/scale/introduce')
	}
	const goRep = () => {
		qryPPVTUserInfo({
			traineeId: userStore.outpatientId
		}).then(res => {
			if (res.data.canPPvt) {
				navigateTo('/pages/introduce/index')
			} else {
				showToast('不在本测评所对应的年龄范围内~')
			}
		})
	}

</script>

<style lang="scss">
	.home {
		width: 100vw;
		height: 100vh;
		background: #F6F6F6;
		padding: 172rpx 50rpx;

		&-pop {
			width: 1190rpx;
			height: 623rpx;
			background: #FFFFFF;
			border-radius: 45rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;
			padding: 0 74rpx;

			&-title {
				font-family: PingFangSC, PingFang SC;
				font-weight: 600;
				font-size: 60rpx;
				color: #111111;
			}

			&-text {
				font-family: PingFangSC, PingFang SC;
				font-weight: 400;
				font-size: 45rpx;
				color: #111111;
				line-height: 75rpx;
			}

			&-btn {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-around;

				&-left {
					width: 468rpx;
					height: 120rpx;
					border-radius: 83rpx;
					border: 4rpx solid #2073FF;
					font-family: SourceHanSansCN, SourceHanSansCN;
					font-weight: bold;
					font-size: 60rpx;
					color: #2073FF;
				}

				&-right {
					width: 468rpx;
					height: 120rpx;
					background: linear-gradient(270deg, #1DA2FF 0%, #2073FF 100%);
					border-radius: 83rpx;
					font-family: SourceHanSansCN, SourceHanSansCN;
					font-weight: bold;
					font-size: 60rpx;
					color: #FFFFFF;
				}
			}
		}

		&-title {
			font-size: 109rpx;
			font-family: PingFangSC-Medium, PingFang SC;
			font-weight: bold;
			color: #333333;
		}

		&-project {
			display: flex;
			align-items: center;
			justify-content: space-between;

			&-box {
				width: 514rpx;
				height: 773rpx;
				background: #FFFFFF;
				box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.1);
				border-radius: 31rpx;
				display: flex;
				flex-direction: column;
				align-items: center;

				&-tips {
					font-family: SourceHanSansCN, SourceHanSansCN;
					font-weight: bold;
					font-size: 36rpx;
					color: #287FFF;
					margin-top: 30rpx;
				}

				&-icon {
					margin-bottom: 78rpx;
					border-radius: 50%;
					width: 391rpx;
					height: 391rpx;
					background: #F9F9F9;
					border: 2rpx solid #EAEAEA;
					color: #FFB699;
					font-size: 150rpx;

					&-img {
						width: 164rpx;
					}
				}

				&-text {
					font-weight: bold;
					font-size: 60rpx;
					color: #111111;
				}

			}
		}
	}
</style>