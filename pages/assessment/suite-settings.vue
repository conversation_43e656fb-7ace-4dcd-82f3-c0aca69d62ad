<template>
	<view class="suite-settings-page">
		<!-- 状态栏 -->
		<view class="status-bar">
			<view class="time">9:41</view>
			<view class="status-icons"></view>
		</view>
		
		<!-- 顶部功能区 -->
		<view class="header">
			<view class="header-bg"></view>
			<view class="back-btn" @click="goBack">
				<image src="/static/nav-back-icon.png" class="back-icon"></image>
			</view>
			<text class="title">套组设置</text>
		</view>
		
		<!-- 主内容区 -->
		<view class="main-content">
			<!-- 左侧套组列表 -->
			<view class="left-sidebar">
				<view class="sidebar-bg"></view>
				
				<!-- 新增套组按钮 -->
                <view class="add-suite-btn" @click="openAddSuite">
					<image src="/static/add-blue-icon.png" class="add-icon" mode="aspectFit"></image>
					<text class="add-text">新增套组</text>
				</view>
				
                <!-- 套组列表 -->
                <view class="suite-list">
                    <view
                        v-for="suite in suiteList"
                        :key="suite.assessmentSetId"
                        class="suite-item"
                        :class="{ active: selectedSuiteId === suite.assessmentSetId }"
                        @click="selectSuite(suite)"
                    >
                        <view class="suite-content">
                            <view class="suite-left">
                                <view class="suite-actions" v-if="selectedSuiteId === suite.assessmentSetId">
                                    <image src="/static/figma-assets/item-edit-icon.png" class="action-icon" mode="aspectFit" @click.stop="onEditSuite(suite)"></image>
                                    <image src="/static/figma-assets/item-delete-icon.png" class="action-icon" mode="aspectFit" @click.stop="onDeleteSuite(suite)"></image>
                                </view>
                                <view v-if="isEditingSuiteName && selectedSuiteId === suite.assessmentSetId" class="suite-name-editor">
                                    <input class="suite-name-input" type="text" v-model="editSuiteName" placeholder="请输入套组名称" @click.stop />
                                    <view v-if="editSuiteName.trim() !== (suite.assessmentSetName || '').trim()" class="unsaved-dot" title="未提交修改"></view>
                                </view>
                                <text v-else class="suite-name">{{ suite.assessmentSetName }}</text>
                                <text v-if="suite.isTemp" class="new-badge">新增</text>
                            </view>
                            <text class="suite-count">
                                {{ getSuiteCounts(suite).current }}项
                                <text v-if="getSuiteCounts(suite).add > 0" style="color:#52C41A">+{{ getSuiteCounts(suite).add }}</text>
                                <text v-if="getSuiteCounts(suite).remove > 0" style="color:#FF4D4F">-{{ getSuiteCounts(suite).remove }}</text>
                                <text v-if="(getSuiteCounts(suite).add + getSuiteCounts(suite).remove) > 0" style="color:#999">（原{{ getSuiteCounts(suite).original }}）</text>
                            </text>
                        </view>
                    </view>
                </view>
			</view>
			
			<!-- 垂直分割线 -->
			<view class="vertical-divider"></view>
			
			<!-- 右侧表格区域 -->
			<view class="right-content">
				<!-- 分类标签页 -->
				<view class="category-tabs-container">
					<view class="category-tabs-bg"></view>
					<view class="category-tabs">
											<view class="category-tab" 
						v-for="(category, index) in categories"
						:key="index"
						:class="{ active: selectedCategory === category.name }"
						@click="switchCategory(category.name)">
						<text class="tab-text">{{ category.name }}</text>
					</view>
					</view>
				</view>
				
				<!-- 表格容器 -->
				<view class="table-container">
					<view class="table-wrapper">
						<!-- 表格头部 -->
						<view class="table-header">
							<view class="table-cell header-cell col-checkbox">
								<view class="checkbox-container">
									<view class="checkbox" 
										:class="{ checked: selectAll, indeterminate: isIndeterminate }"
										@click="toggleSelectAll">
										<view class="checkbox-inner">
											<view v-if="selectAll && !isIndeterminate" class="check-icon"></view>
											<view v-if="isIndeterminate" class="minus-icon"></view>
										</view>
									</view>
									<text class="checkbox-label">全选</text>
								</view>
							</view>
							<view class="table-cell header-cell col-index">序号</view>
							<view class="table-cell header-cell col-project">项目</view>
						</view>
						
						<!-- 表格内容 -->
						<view class="table-body">
							<view class="table-row" 
								v-for="(item, index) in currentTableData" 
								:key="index"
								:class="{ selected: item.selected }"
								@click="toggleItemSelect(index)">
								<!-- 复选框列 -->
								<view class="table-cell col-checkbox">
									<view class="checkbox" 
										:class="{ checked: item.selected }"
										@click.stop="toggleItemSelect(index)">
										<view class="checkbox-inner">
											<view v-if="item.selected" class="check-icon"></view>
										</view>
									</view>
								</view>
								<!-- 序号列 -->
								<view class="table-cell col-index">{{ item.index }}</view>
								<!-- 项目列 -->
								<view class="table-cell col-project">
									<view class="project-info">
										<text class="project-name">{{ item.projectName }}</text>
										<view class="project-tag" :class="getCategoryClass(item.categoryId)">
											<text class="tag-text">{{ item.typeName }}</text>
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 底部操作区 -->
				<view class="footer">
					<view class="footer-buttons">
						<view class="btn-cancel" @click="goBack">
							<text class="btn-text">取消</text>
						</view>
						<view class="btn-submit" @click="submitSuiteSettings">
							<text class="btn-text">确定</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
    <!-- 自定义确认弹窗（参考Figma样式） -->
    <view v-if="showConfirmModal" class="confirm-modal-mask" @click="cancelSubmit"></view>
    <view v-if="showConfirmModal" class="confirm-modal">
        <view class="confirm-modal-header">
            <text class="confirm-title">确认提交</text>
        </view>
        <view class="confirm-modal-body">
            <text class="confirm-content">是否保存当前套组名称与项目配置？</text>
        </view>
        <view class="confirm-modal-footer">
            <view class="btn ghost" @click="cancelSubmit"><text class="btn-text">取消</text></view>
            <view class="btn primary" :class="{ loading: confirming }" @click="confirmSubmit">
                <text class="btn-text">{{ confirming ? '提交中...' : '确定' }}</text>
            </view>
        </view>
    </view>

    <!-- 新增套组弹窗（复用样式体系） -->
    <view v-if="showAddModal" class="confirm-modal-mask" @click="closeAddSuite"></view>
    <view v-if="showAddModal" class="confirm-modal">
        <view class="confirm-modal-header">
            <text class="confirm-title">新增套组</text>
        </view>
        <view class="confirm-modal-body">
            <input class="suite-name-input" v-model="newSuiteName" placeholder="请输入套组名称" />
        </view>
        <view class="confirm-modal-footer">
            <view class="btn ghost" @click="closeAddSuite"><text class="btn-text">取消</text></view>
            <view class="btn primary" :class="{ loading: adding }" @click="confirmAddSuite">
                <text class="btn-text">{{ adding ? '提交中...' : '确定' }}</text>
            </view>
        </view>
    </view>

    <!-- 删除确认弹窗（样式一致） -->
    <view v-if="showDeleteModal" class="confirm-modal-mask" @click="cancelDelete"></view>
    <view v-if="showDeleteModal" class="confirm-modal">
        <view class="confirm-modal-header">
            <text class="confirm-title">确认删除</text>
        </view>
        <view class="confirm-modal-body">
            <text class="confirm-content">是否删除当前套组？删除后不可恢复。</text>
        </view>
        <view class="confirm-modal-footer">
            <view class="btn ghost" @click="cancelDelete"><text class="btn-text">取消</text></view>
            <view class="btn primary" :class="{ loading: deleting }" @click="confirmDelete">
                <text class="btn-text">{{ deleting ? '删除中...' : '确定' }}</text>
            </view>
        </view>
    </view>
</template>

<script>
import { qryAssessmentSetTypeList, qryAssessmentSetProjectList, qryAssessmentSetList, qryAssessmentSetDetail, updateAssessmentSet, addAssessmentSet, deleteAssessmentSet } from '@/service/assessment.js';

export default {
	data() {
		return {
			// 套组相关
			suiteList: [],
			selectedSuiteId: '',
			selectedSuite: null,
			editSuiteName: '',
			isEditingSuiteName: false,
			isLoadingSuites: false,
            // 项目选中基准与未提交变更映射（按套组）
            suiteBaseProjectIdsMap: {}, // { [assessmentSetId]: string[] }
            suitePendingProjectIdsMap: {}, // { [assessmentSetId]: string[] }
			// 新增套组弹窗
			showAddModal: false,
			adding: false,
			newSuiteName: '',
            // 自定义确认弹窗
            showConfirmModal: false,
            confirming: false,
            pendingPayload: null,
            // 删除确认弹窗
            showDeleteModal: false,
            deleting: false,
            pendingDeleteId: '',
			selectedCategory: '全部分类',
			selectedCategoryId: '', // 当前选中分类的ID
			selectAll: false,
			categories: [
				{ name: '全部分类', typeId: '' }
			], // 动态分类数据，包含ID和名称
			tableData: [], // 当前显示的项目列表数据
            selectedProjectIds: [], // 选中的项目ID集合（数组存储，避免 Set 引起白屏）
			allProjectsData: [] // 所有项目数据的缓存
		}
	},
	computed: {
		currentTableData() {
			// 为每个项目添加序号和选择状态
            return this.tableData.map((item, index) => ({
				...item,
				index: index + 1,
                selected: this.selectedProjectIds.includes(item.projectId)
			}))
		},
		selectedCount() {
			return this.currentTableData.filter(item => item.selected).length
		},
		totalCount() {
			return this.currentTableData.length
		},
		isIndeterminate() {
			const selectedCount = this.selectedCount
			return selectedCount > 0 && selectedCount < this.currentTableData.length
		}
	},
	watch: {
		selectedCount(newVal) {
			this.selectAll = newVal === this.currentTableData.length && newVal > 0
		}
	},
	async onLoad() {
		console.log('套组设置页面加载完成')
		await this.loadSuites()
		await this.loadCategories()
        await this.loadProjectList() // 默认加载全部分类的项目
        await this.syncSelectedProjectsWithSuite() // 根据默认选中的套组，同步右侧选中项
	},
	methods: {
		// 加载套组列表
		async loadSuites() {
			this.isLoadingSuites = true
			try {
				const response = await qryAssessmentSetList({})
				console.log('获取套组列表:', response)
				if (response && Array.isArray(response.data)) {
					this.suiteList = response.data
				} else if (response && response.data) {
					this.suiteList = Array.isArray(response.data) ? response.data : []
				} else {
					this.suiteList = []
				}
				this.selectedSuite = this.suiteList[0] || null
				this.selectedSuiteId = this.selectedSuite ? this.selectedSuite.assessmentSetId : ''
                this.editSuiteName = this.selectedSuite ? this.selectedSuite.assessmentSetName : ''
                this.isEditingSuiteName = false
			} catch (error) {
				console.error('获取套组列表失败:', error)
				uni.showToast({ title: '获取套组列表失败', icon: 'none' })
			} finally {
				this.isLoadingSuites = false
			}
		},
        // 根据当前选中的套组，同步右侧勾选项
        async syncSelectedProjectsWithSuite() {
            if (!this.selectedSuiteId) return
            // 临时套组不请求后端详情，基准视为空集
            if (String(this.selectedSuiteId).startsWith('temp_')) {
                if (!this.suiteBaseProjectIdsMap) this.$set(this, 'suiteBaseProjectIdsMap', {})
                this.$set(this.suiteBaseProjectIdsMap, this.selectedSuiteId, new Set())
                // 使用未提交或空
                if (this.suitePendingProjectIdsMap && this.suitePendingProjectIdsMap[this.selectedSuiteId]) {
                    this.selectedProjectIds = [...this.suitePendingProjectIdsMap[this.selectedSuiteId]]
                } else {
                    this.selectedProjectIds = []
                }
                return
            }
            try {
                const res = await qryAssessmentSetDetail({ assessmentSetId: this.selectedSuiteId })
                const detail = res && res.data ? res.data : null
                const projectIds = detail && Array.isArray(detail.simpleProjectInfoList)
                    ? detail.simpleProjectInfoList.map(p => p.projectId)
                    : []
                // 基准集合存储
                if (!this.suiteBaseProjectIdsMap) this.$set(this, 'suiteBaseProjectIdsMap', {})
                this.$set(this.suiteBaseProjectIdsMap, this.selectedSuiteId, new Set(projectIds))
                // 如果该套组已有未提交的选择，则使用未提交的；否则使用基准
                if (this.suitePendingProjectIdsMap && this.suitePendingProjectIdsMap[this.selectedSuiteId]) {
                    this.selectedProjectIds = [...this.suitePendingProjectIdsMap[this.selectedSuiteId]]
                } else {
                    this.selectedProjectIds = [...projectIds]
                }
            } catch (e) {
                console.error('获取套组详情失败:', e)
                uni.showToast({ title: '获取套组详情失败', icon: 'none' })
            }
        },
		// 加载分类数据
		async loadCategories() {
			try {
				const response = await qryAssessmentSetTypeList()
				console.log('获取分类数据:', response)
				
				if (response && response.data) {
					// 处理接口返回的分类数据，保留"全部分类"在第一位
					const categoryList = response.data.map(item => ({
						name: item.typeName ? item.typeName.trim() : (item.name || item.categoryName),
						typeId: item.typeId || item.id
					}))
					this.categories = [{ name: '全部分类', typeId: '' }, ...categoryList]
				}
			} catch (error) {
				console.error('获取分类数据失败:', error)
				uni.showToast({
					title: '获取分类数据失败',
					icon: 'none'
				})
			}
		},
		// 加载项目列表数据
		async loadProjectList(typeId = '') {
			try {
				const response = await qryAssessmentSetProjectList({ typeId })
				console.log('获取项目列表数据:', response)
				
				if (response && response.data) {
					this.tableData = response.data.map(item => ({
						projectId: item.projectId,
						projectName: item.projectName,
						typeName: item.typeName,
						categoryId: item.categoryId,
						typeId: item.typeId
					}))
					
					// 如果是加载全部分类的数据，缓存起来
					if (typeId === '') {
						this.allProjectsData = [...this.tableData]
					}
				}
			} catch (error) {
				console.error('获取项目列表失败:', error)
				uni.showToast({
					title: '获取项目列表失败',
					icon: 'none'
				})
			}
		},
		goBack() {
			uni.navigateBack()
		},
        selectSuite(suite) {
            this.selectedSuite = suite
            this.selectedSuiteId = suite.assessmentSetId
            this.editSuiteName = suite.assessmentSetName
            this.isEditingSuiteName = false
            // 重置全选状态和分类选择
            this.selectAll = false
            this.selectedCategory = '全部分类'
            this.selectedCategoryId = ''
            // 重新加载全部分类的项目
            this.loadProjectList('').then(() => {
                this.syncSelectedProjectsWithSuite()
            })
        },
		onEditSuite(suite) {
			this.selectedSuite = suite
			this.selectedSuiteId = suite.assessmentSetId
			this.editSuiteName = suite.assessmentSetName
			this.isEditingSuiteName = true
		},
        onDeleteSuite(suite) {
            this.pendingDeleteId = suite.assessmentSetId
            this.showDeleteModal = true
        },
        async confirmDelete() {
            if (!this.pendingDeleteId) return
            this.deleting = true
            try {
                await deleteAssessmentSet({ assessmentSetId: this.pendingDeleteId })
                this.showDeleteModal = false
                this.pendingDeleteId = ''
                // 刷新列表
                await this.loadSuites()
                // 刷新右侧为默认选中套组的数据
                await this.loadProjectList('')
                await this.syncSelectedProjectsWithSuite()
                uni.showToast({ title: '删除成功', icon: 'success' })
            } catch (e) {
                console.error('删除套组失败:', e)
                uni.showToast({ title: '删除失败', icon: 'none' })
            } finally {
                this.deleting = false
            }
        },
        cancelDelete() {
            this.showDeleteModal = false
            this.pendingDeleteId = ''
        },
		// 右下角确定提交
        async submitSuiteSettings() {
            const finalName = this.isEditingSuiteName ? (this.editSuiteName || '').trim() : (this.selectedSuite?.assessmentSetName || '')
            if (!finalName) {
                uni.showToast({ title: '套组名称不能为空', icon: 'none' })
                return
            }
            // 去重，确保不提交重复的 projectId
            const dedupIds = Array.from(new Set(this.selectedProjectIds))
            this.pendingPayload = {
                assessmentSetId: this.selectedSuiteId,
                assessmentSetName: finalName,
                projectIds: dedupIds
            }
            this.showConfirmModal = true
        },
        async confirmSubmit() {
            if (!this.pendingPayload) return
            this.confirming = true
            try {
                // 如果是临时套组则调用新增接口，否则调用更新接口
                if (String(this.selectedSuiteId).startsWith('temp_')) {
                    await addAssessmentSet(this.pendingPayload)
                    // 新增成功后刷新列表并选中新创建名称
                    await this.loadSuites()
                    const created = this.suiteList.find(s => s.assessmentSetName === this.pendingPayload.assessmentSetName)
                    if (created) {
                        this.selectSuite(created)
                    }
                } else {
                    await updateAssessmentSet(this.pendingPayload)
                    const idx = this.suiteList.findIndex(s => s.assessmentSetId === this.selectedSuiteId)
                    if (idx !== -1) {
                        this.$set(this.suiteList, idx, { ...this.suiteList[idx], assessmentSetName: this.pendingPayload.assessmentSetName })
                    }
                    this.selectedSuite = this.suiteList[idx] || this.selectedSuite
                    // 提交成功后将未提交集合写入为基准，并更新计数
                    const finalSet = new Set(this.selectedProjectIds)
                    if (!this.suiteBaseProjectIdsMap) this.$set(this, 'suiteBaseProjectIdsMap', {})
                    this.$set(this.suiteBaseProjectIdsMap, this.selectedSuiteId, finalSet)
                    if (this.suitePendingProjectIdsMap && this.suitePendingProjectIdsMap[this.selectedSuiteId]) {
                        const { [this.selectedSuiteId]: _omit, ...rest } = this.suitePendingProjectIdsMap
                        this.suitePendingProjectIdsMap = rest
                    }
                    const idx2 = this.suiteList.findIndex(s => s.assessmentSetId === this.selectedSuiteId)
                    if (idx2 !== -1) {
                        this.$set(this.suiteList, idx2, { ...this.suiteList[idx2], projectTotal: finalSet.size, isTemp: false })
                    }
                }
                this.isEditingSuiteName = false
                this.showConfirmModal = false
                this.pendingPayload = null
                uni.showToast({ title: '保存成功', icon: 'success' })
            } catch (e) {
                console.error('保存套组失败:', e)
                uni.showToast({ title: '保存失败', icon: 'none' })
            } finally {
                this.confirming = false
            }
        },
        // 获取左侧套组的计数（当前/新增/移除/原有）
        getSuiteCounts(suite) {
            const id = suite.assessmentSetId
            const baseSet = this.suiteBaseProjectIdsMap && this.suiteBaseProjectIdsMap[id] ? this.suiteBaseProjectIdsMap[id] : null
            const pendingSet = this.suitePendingProjectIdsMap && this.suitePendingProjectIdsMap[id]
                ? this.suitePendingProjectIdsMap[id]
                : null
            const original = baseSet ? baseSet.size : (suite.projectTotal || 0)
            // 若既无基准、也无未提交，则直接展示原始 projectTotal
            if (!baseSet && !pendingSet) {
                return { current: original, add: 0, remove: 0, original }
            }
            // 否则按已有集合计算
            const effectiveSet = pendingSet ? pendingSet : baseSet
            const current = effectiveSet.size
            let add = 0, remove = 0
            if (baseSet) {
                const compareSet = pendingSet ? pendingSet : baseSet
                compareSet.forEach(pid => { if (!baseSet.has(pid)) add++ })
                baseSet.forEach(pid => { if (!compareSet.has(pid)) remove++ })
            } else {
                // 无基准（例如初始未加载或临时套组）
                add = Math.max(current - original, 0)
                remove = 0
            }
            return { current, add, remove, original }
        },
        cancelSubmit() {
            this.showConfirmModal = false
            this.pendingPayload = null
        },
        async switchCategory(categoryName) {
			this.selectedCategory = categoryName
			
			// 找到对应的分类ID
			const categoryData = this.categories.find(item => item.name === categoryName)
			if (categoryData) {
				this.selectedCategoryId = categoryData.typeId
				// 加载对应分类的项目列表
				await this.loadProjectList(categoryData.typeId)
			}
			
			// 重置全选状态
			this.selectAll = false
		},
        toggleSelectAll() {
			const shouldSelectAll = !this.selectAll || this.isIndeterminate
            const ids = shouldSelectAll ? this.currentTableData.map(i => i.projectId) : []
            this.selectedProjectIds = ids
			this.selectAll = shouldSelectAll
            // 写入未提交映射
            if (!this.suitePendingProjectIdsMap) this.$set(this, 'suitePendingProjectIdsMap', {})
            this.$set(this.suitePendingProjectIdsMap, this.selectedSuiteId, new Set(this.selectedProjectIds))
		},
        toggleItemSelect(index) {
			const item = this.currentTableData[index]
            const exists = this.selectedProjectIds.includes(item.projectId)
            this.selectedProjectIds = exists
                ? this.selectedProjectIds.filter(id => id !== item.projectId)
                : [...this.selectedProjectIds, item.projectId]
            // 写入未提交映射
            if (!this.suitePendingProjectIdsMap) this.$set(this, 'suitePendingProjectIdsMap', {})
            this.$set(this.suitePendingProjectIdsMap, this.selectedSuiteId, new Set(this.selectedProjectIds))
		},
		getCategoryClass(categoryId) {
			// categoryId为0代表评估类型（蓝色），categoryId为1代表检测类型（黄色）
			return categoryId === '0' || categoryId === 0 ? 'assessment' : 'detection'
		},
        openAddSuite() {
            this.newSuiteName = ''
            this.showAddModal = true
        },
        closeAddSuite() {
            this.showAddModal = false
        },
        async confirmAddSuite() {
            const name = (this.newSuiteName || '').trim()
            if (!name) {
                uni.showToast({ title: '请输入套组名称', icon: 'none' })
                return
            }
            // 本地新增一条临时套组，未提交到后端
            const tempId = `temp_${Date.now()}`
            const tempSuite = { assessmentSetId: tempId, assessmentSetName: name, projectTotal: 0, isTemp: true }
            this.suiteList = [tempSuite, ...this.suiteList]
            // 默认不选中任何内容
            this.selectedSuite = tempSuite
            this.selectedSuiteId = tempId
            this.editSuiteName = name
            this.isEditingSuiteName = true
            this.selectedProjectIds = [] // 清空所选项目
            // 初始化映射
            if (!this.suiteBaseProjectIdsMap) this.$set(this, 'suiteBaseProjectIdsMap', {})
            if (!this.suitePendingProjectIdsMap) this.$set(this, 'suitePendingProjectIdsMap', {})
            this.$set(this.suiteBaseProjectIdsMap, tempId, new Set())
            this.$set(this.suitePendingProjectIdsMap, tempId, new Set())
            this.showAddModal = false
            uni.showToast({ title: '已新增（未提交）', icon: 'none' })
        },

	}
}
</script>

<style lang="scss" scoped>
.suite-settings-page {
	width: 100vw;
	height: 100vh;
	background: #F6F6F6;
	overflow: hidden;
}

/* 自定义确认弹窗样式（参考Figma） */
.confirm-modal-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.35);
  z-index: 10000;
}

.confirm-modal {
  position: fixed;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 420px;
  background: #FFFFFF;
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0,0,0,0.12);
  z-index: 10001;
  overflow: hidden;
}

.confirm-modal-header {
  padding: 18px 20px 0 20px;
  text-align: center;
}
.confirm-title {
  font-family: 'Alibaba PuHuiTi';
  font-size: 20px;
  color: #333333;
}

.confirm-modal-body {
  padding: 12px 20px 0 20px;
}
.confirm-content {
  font-family: 'Alibaba PuHuiTi';
  font-size: 16px;
  color: #666666;
}

.confirm-modal-footer {
  display: flex;
  justify-content: space-between; /* 左右两侧分布 */
  align-items: center;
  padding: 20px;
}
.confirm-modal-footer .btn {
  min-width: 92px;
  height: 36px;
  padding: 0 16px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.confirm-modal-footer .btn .btn-text {
  font-family: 'Alibaba PuHuiTi';
  font-size: 16px;
}
.confirm-modal-footer .btn.ghost {
  background: #FFFFFF;
  border: 1px solid #287FFF;
}
.confirm-modal-footer .btn.ghost .btn-text {
  color: #287FFF;
}
.confirm-modal-footer .btn.primary {
  background: #287FFF;
}
.confirm-modal-footer .btn.primary .btn-text {
  color: #FFFFFF;
}
.confirm-modal-footer .btn.primary.loading {
  opacity: 0.7;
}

/* 状态栏 */
.status-bar {
	width: 100%;
	height: 44px;
	background: #FFFFFF;
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0 20px;
	
	.time {
		font-family: 'Alibaba PuHuiTi';
		font-size: 26px;
		color: #333333;
	}
	
	.status-icons {
		display: flex;
		align-items: center;
		gap: 50px;
	}
}

/* 顶部功能区 */
.header {
	position: relative;
	width: 100%;
	height: 82px;
	
	.header-bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: #FFFFFF;
	}
	
	.back-btn {
		position: absolute;
		left: 37px;
		top: 26px;
		width: 30px;
		height: 30px;
		display: flex;
		align-items: center;
		justify-content: center;
		
		.back-icon {
			width: 24px;
			height: 24px;
		}
	}
	
	.title {
		position: absolute;
		left: 50%;
		top: 26px;
		transform: translateX(-50%);
		font-family: 'Alibaba PuHuiTi';
		font-size: 30px;
		color: #333333;
		line-height: 30px;
	}
}

/* 主内容区 */
.main-content {
	display: flex;
	height: calc(100vh - 126px - 97px - 40px); /* 减去footer高度(97px)和底部间距(40px) */
	margin: 28px 37px 0 37px;
}

/* 左侧分类列表 */
.left-sidebar {
	position: relative;
	width: 326px; /* 原245px基础上增加约1/3 */
	height: 100%;
	
	.sidebar-bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: #FFFFFF;
		border-radius: 15px 0 0 15px;
	}
	
	.suite-list {
		position: relative;
		padding: 16px 0 0 8px;
		height: calc(100% - 120px);
		
    .suite-item {
            display: flex;
            align-items: center;
            gap: 11px;
        padding: 12px 12px;
            margin-bottom: 22px;
            width: calc(100% - 16px);
        min-height: 44px;
            border-radius: 3px;
            cursor: pointer;
			
			&.active {
				background: rgba(40, 127, 255, 0.1);
				
				.suite-name {
					color: #287FFF;
					font-weight: 500;
				}
			}
			
		.suite-content {
				display: flex;
			justify-content: space-between;
				align-items: center;
				flex: 1;
			gap: 12px;
			}
			
		.suite-left {
			display: flex;
			align-items: center;
			gap: 8px;
			flex: 1;
			overflow: hidden;
		}

            .suite-actions {
                display: flex;
                align-items: center;
                gap: 18px; /* 原6px的3倍 */
            }

		.action-icon {
			width: 18px;
			height: 18px;
		}

		.suite-count {
			font-family: 'Alibaba PuHuiTi';
			font-size: 14px;
			color: #666666;
			white-space: nowrap;
		}

        .new-badge {
            margin-left: 6px;
            font-family: 'Alibaba PuHuiTi';
            font-size: 12px;
            color: #FF4D4F;
            background: rgba(255,77,79,0.08);
            border: 1px solid rgba(255,77,79,0.35);
            border-radius: 3px;
            padding: 1px 4px;
        }

            .suite-name {
                font-family: 'Alibaba PuHuiTi';
                font-size: 17px;
                color: #333333;
                line-height: 22px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }

        .suite-name-editor {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            min-width: 0;
        }

        .suite-name-input {
            flex: 1;
            min-width: 0;
            height: 28px;
            padding: 0 8px;
            border: 1px solid #C7C7C7;
            border-radius: 4px;
            font-family: 'Alibaba PuHuiTi';
            font-size: 16px;
            color: #333333;
            background: #FFFFFF;
        }

        .unsaved-dot {
            width: 8px;
            height: 8px;
            background: #FFB130;
            border-radius: 50%;
        }
		}
	}
	
	.add-suite-btn {
		position: relative;
		top: 8px;
		left: 8px;
		display: flex;
		align-items: center;
		gap: 11px;
		width: 229px;
		height: 33px;
		cursor: pointer;
		margin-bottom: 12px;
		
		.add-icon {
			width: 22px;
			height: 22px;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.add-text {
			font-family: 'Alibaba PuHuiTi';
			font-size: 17px;
			color: #287FFF;
			line-height: 17px;
			display: flex;
			align-items: center;
			margin-top: 2px;
		}
	}
}

/* 垂直分割线 */
.vertical-divider {
	width: 1px;
	height: 100%;
	background: #EEEEEE;
	flex-shrink: 0;
}

/* 右侧内容区 */
.right-content {
	flex: 1;
	display: flex;
	flex-direction: column;
}

/* 分类标签页 */
.category-tabs-container {
	position: relative;
	width: 100%;
	height: 50px;
	margin-bottom: 4px;
	
	.category-tabs-bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: #FFFFFF;
		border-radius: 0 15px 0 0;
	}
	
	.category-tabs {
		position: relative;
		display: flex;
		align-items: flex-end;
		padding: 8px 8px 0 8px;
		height: 100%;
		
		.category-tab {
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 8px 11px;
			margin-right: 7px;
			background: #F3F8FF;
			border-radius: 6px 6px 0 0;
			cursor: pointer;
			
			&.active {
				background: #287FFF;
				
				.tab-text {
					color: #FFFFFF;
				}
			}
			
			.tab-text {
				font-family: 'Alibaba PuHuiTi';
				font-size: 17px;
				color: #287FFF;
				line-height: 17px;
				white-space: nowrap;
			}
		}
	}
}

/* 表格容器 */
.table-container {
	flex: 1;
	background: #FFFFFF;
	border-radius: 0 0 0 0;
	border: 1px solid #EEEEEE;
	border-left: none;
	border-top: none;
	overflow: hidden;
	display: flex;
	flex-direction: column;
}

.table-wrapper {
	flex: 1;
	overflow: hidden;
}

.table-header {
	display: flex;
	background: #F6F6F6;
	border-bottom: 1px solid #EEEEEE;
	
	.header-cell {
		font-family: 'Alibaba PuHuiTi';
		font-weight: 400;
		font-size: 22px;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 15px 11px;
		border-right: 1px solid #EEEEEE;
		
		&:last-child {
			border-right: none;
		}
	}
}

.table-body {
	.table-row {
		display: flex;
		cursor: pointer;
		transition: background-color 0.2s;
		
		&:not(:last-child) {
			border-bottom: 1px solid #EEEEEE;
		}
		
		&:hover {
			background-color: #F8F9FA;
		}
		
		&.selected {
			background-color: #F3F8FF;
		}
	}
}

.table-cell {
	font-family: 'Alibaba PuHuiTi';
	font-size: 22px;
	color: #333333;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 8px 11px;
	border-right: 1px solid #EEEEEE;
	min-height: 61px;
	
	&:last-child {
		border-right: none;
	}
}

/* 列宽设置 */
.col-checkbox {
	width: 113px;
	
	.checkbox-container {
		display: flex;
		align-items: center;
		gap: 7px;
		
		.checkbox-label {
			font-family: 'Alibaba PuHuiTi';
			font-size: 22px;
			color: #333333;
			line-height: 22px;
		}
	}
	
	.checkbox {
		width: 15px;
		height: 15px;
		border: 2px solid #C7C7C7;
		border-radius: 2px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #FFFFFF;
		
		&.checked {
			background: #287FFF;
			border-color: #287FFF;
		}
		
		&.indeterminate {
			background: #287FFF;
			border-color: #287FFF;
		}
		
		.checkbox-inner {
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.check-icon {
				width: 9px;
				height: 6px;
				position: relative;
				
				&::after {
					content: '';
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%) rotate(-45deg);
					width: 9px;
					height: 3px;
					border: 2px solid #FFFFFF;
					border-top: none;
					border-right: none;
				}
			}
			
			.minus-icon {
				width: 9px;
				height: 2px;
				background: #FFFFFF;
			}
		}
	}
}

.col-index {
	width: 146px;
}

.col-project {
	flex: 1;
	justify-content: flex-start;
	padding-left: 15px;
	
	.project-info {
		display: flex;
		align-items: center;
		gap: 15px;
		width: 100%;
		
		.project-name {
			font-family: 'Alibaba PuHuiTi';
			font-size: 22px;
			color: #333333;
			line-height: 22px;
		}
		
		.project-tag {
			padding: 7px 15px;
			border-radius: 7px;
			
			&.assessment {
				background: #EBF2FF;
				
				.tag-text {
					color: #287FFF;
				}
			}
			
			&.detection {
				background: #FFEFD2;
				
				.tag-text {
					color: #FFB130;
				}
			}
			
			.tag-text {
				font-family: 'Alibaba PuHuiTi';
				font-size: 22px;
				line-height: 22px;
			}
		}
	}
}

/* 底部操作区 */
.footer {
	position: fixed;
	bottom: 40px; /* 距离屏幕底部40px */
	right: 37px; /* 与主内容区右边距保持一致 */
	display: flex;
	align-items: center;
	justify-content: flex-end;
	gap: 8px;
	padding: 17px 28px;
	background: transparent; /* 移除背景色 */
	border: none; /* 移除边框 */
	flex-shrink: 0;
	z-index: 10; /* 确保按钮在最上层 */
	
	.footer-buttons {
		display: flex;
		gap: 8px;
		
		.btn-cancel {
			width: 100px;
			height: 40px;
			background: #FFFFFF;
			border: 1.4px solid #287FFF;
			border-radius: 6px;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.btn-text {
				font-family: 'Alibaba PuHuiTi';
				font-size: 17px;
				color: #287FFF;
				line-height: 17px;
			}
		}
		
		.btn-submit {
			width: 100px;
			height: 40px;
			background: #287FFF;
			border-radius: 6px;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.btn-text {
				font-family: 'Alibaba PuHuiTi';
				font-size: 17px;
				color: #FFFFFF;
				line-height: 17px;
			}
		}
	}
}
</style>