<template>
	<view class="add-order-page">
		<!-- 状态栏 -->
		<view class="status-bar">
			<view class="status-icons">
				<!-- 状态栏图标区域 -->
			</view>
		</view>
		
		<!-- 顶部功能区 -->
		<view class="header">
			<view class="header-bg"></view>
			<view class="back-btn" @click="goBack">
				<image src="/static/nav-back-icon.png" class="back-icon"></image>
			</view>
			<text class="title">新增工单</text>
		</view>
		
		<!-- 分类选项卡区域 -->
		<view class="category-tabs-container">
			<view class="category-tabs-bg"></view>
			<view class="category-tabs">
				<view class="category-tab" 
					v-for="(category, index) in categories"
					:key="index"
					:class="{ active: activeCategory === category.name }"
					@click="switchCategory(category.name)">
					<text class="tab-text">{{ category.name }}</text>
				</view>
			</view>
			<!-- 套组设置区域 -->
            <view class="suite-settings">
				<view class="suite-settings-btn" @click="goToSuiteSettings">
					<image src="/static/group-set-icon.png" class="settings-icon"></image>
					<text class="settings-text">套组设置</text>
				</view>
              <view class="suite-dropdown" @click="toggleSuiteDropdown">
                <text class="suite-text">{{ selectedSuite ? selectedSuite.assessmentSetName : (isLoadingSuites ? '加载中...' : '请选择套组') }}</text>
                <view class="dropdown-arrow"></view>
              </view>
              <view v-if="showSuiteDropdown" class="suite-options">
                <view v-if="isLoadingSuites" class="suite-option loading">
                  <text class="suite-option-text">加载中...</text>
                </view>
                <template v-else>
                  <!-- 顶部新增：不选择套组 -->
                  <view class="suite-option" @click.stop="selectSuite(null)">
                    <text class="suite-option-text">不选择套组</text>
                    <text v-if="!selectedSuite" class="suite-option-check">✓</text>
                  </view>
                  <view v-if="suiteList.length === 0" class="suite-option empty">
                    <text class="suite-option-text">暂无套组</text>
                  </view>
                  <view v-else v-for="suite in suiteList" :key="suite.assessmentSetId" class="suite-option" @click.stop="selectSuite(suite)">
                    <text class="suite-option-text">{{ suite.assessmentSetName }}</text>
                    <text v-if="selectedSuite && selectedSuite.assessmentSetId === suite.assessmentSetId" class="suite-option-check">✓</text>
                  </view>
                </template>
              </view>
              <view v-if="showSuiteDropdown" class="suite-overlay" @click="showSuiteDropdown = false"></view>
			</view>
			<!-- 患者搜索区域 -->
			<view class="patient-search">
				<text class="search-label">患       者：</text>
				<view class="search-input-frame" :class="{ 'search-focused': showPatientDropdown }">
					<input 
						type="text" 
						placeholder="请输入患者名字/病案号查找" 
						class="search-input" 
						v-model="searchKeyword" 
						@input="onPatientSearch"
						@focus="onSearchFocus"
						@blur="onSearchBlur"
					/>
					<view class="search-addon" @click="searchPatients">
						<view class="search-icon"></view>
					</view>
				</view>
				<!-- 患者搜索结果浮动窗口 -->
				<view v-if="showPatientDropdown && patientList.length > 0" class="patient-dropdown">
					<view 
						v-for="(patient, index) in patientList" 
						:key="patient.traineeId || index"
						class="patient-item"
						@click="selectPatient(patient)"
					>
						<text class="patient-info">{{ patient.traineeSimDesc }}</text>
					</view>
				</view>
				<!-- 无搜索结果提示 -->
				<view v-if="showPatientDropdown && searchKeyword && patientList.length === 0 && !isSearching" class="no-result-tip">
					<text class="no-result-text">未找到相关患者</text>
				</view>
				<!-- 搜索加载提示 -->
				<view v-if="isSearching" class="search-loading">
					<text class="loading-text">搜索中...</text>
				</view>
			</view>
		</view>
		
		<!-- 表格和分页 -->
		<view class="table-container">
			<view class="table-wrapper">
				<!-- 表格头部 -->
				<view class="table-header">
					<!-- 全选列 -->
					<view class="table-cell header-cell col-checkbox">
						<view class="checkbox-container">
							<view class="checkbox" 
								:class="{ checked: selectAll, indeterminate: isIndeterminate }"
								@click="toggleSelectAll">
								<view class="checkbox-inner">
									<view v-if="selectAll && !isIndeterminate" class="check-icon"></view>
									<view v-if="isIndeterminate" class="minus-icon"></view>
								</view>
							</view>
							<text class="checkbox-label">全选</text>
						</view>
					</view>
					<!-- 序号列 -->
					<view class="table-cell header-cell col-index">序号</view>
					<!-- 项目列 -->
					<view class="table-cell header-cell col-project">项目</view>
				</view>
				
				<!-- 表格内容 -->
				<view class="table-body">
                    <view class="table-row" 
						v-for="(item, index) in currentTableData" 
						:key="index"
                        :class="{ selected: item.selected, 'changed-added': item.addedComparedToSuite, 'changed-removed': item.removedComparedToSuite }"
						@click="toggleItemSelect(index)">
						<!-- 复选框列 -->
						<view class="table-cell col-checkbox">
							<view class="checkbox" 
								:class="{ checked: item.selected }"
								@click.stop="toggleItemSelect(index)">
								<view class="checkbox-inner">
									<view v-if="item.selected" class="check-icon"></view>
								</view>
							</view>
						</view>
						<!-- 序号列 -->
						<view class="table-cell col-index">{{ item.index }}</view>
						<!-- 项目列 -->
						<view class="table-cell col-project">
							<view class="project-info">
                                <text class="project-name">{{ item.projectName }}</text>
								<view class="project-tag" :class="getCategoryClass(item.categoryId)">
									<text class="tag-text">{{ item.typeName }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 底部操作区 -->
			<view class="footer">
				<text class="total-count">共{{ totalCount }}条</text>
				<view class="footer-buttons">
					<view class="btn-cancel" @click="goBack">
						<text class="btn-text">取消</text>
					</view>
					<view class="btn-submit" @click="submitOrder">
						<text class="btn-text">确定提交</text>
					</view>
				</view>
			</view>
		</view>
	</view>
	
	<!-- 更换套组确认弹窗 -->
	<view v-if="showChangeConfirmModal" class="confirm-modal-mask" @click="cancelChangeSuite"></view>
	<view v-if="showChangeConfirmModal" class="confirm-modal">
		<view class="confirm-modal-header">
			<text class="confirm-title">确认更换套组</text>
		</view>
		<view class="confirm-modal-body">
			<text class="confirm-content">如果更换套组后，之前的修改将丢失</text>
		</view>
		<view class="confirm-modal-footer">
			<view class="btn ghost" @click="cancelChangeSuite"><text class="btn-text">取消</text></view>
			<view class="btn primary" @click="confirmChangeSuite"><text class="btn-text">确定</text></view>
		</view>
	</view>
</template>

<script>
    import { qryAssessmentSetTypeList, qryAssessmentSetProjectList, qryAssessmentSetList, qryAssessmentSetDetail } from '@/service/assessment.js'
    import { qryTraineeList } from '@/service/index.js'
    import { addWorkOrder } from '@/service/workorder.js'
	
	export default {
		data() {
			return {
				activeCategory: '全部分类',
				activeCategoryId: '', // 当前选中分类的ID，空字符串表示全部分类
				searchKeyword: '',
				selectAll: false,
				categories: [
					{ name: '全部分类', typeId: '' }
				], // 动态分类数据，包含ID和名称
				tableData: [], // 项目列表数据
				selectedProjectIds: new Set(), // 选中的项目ID集合
				allProjectsData: [], // 所有项目数据的缓存
				// 患者搜索相关
				patientList: [], // 患者搜索结果列表
				showPatientDropdown: false, // 是否显示患者下拉列表
				isSearching: false, // 是否正在搜索
				selectedPatient: null, // 选中的患者信息
                searchTimer: null, // 搜索防抖定时器
                // 套组选择相关
                suiteList: [],
                selectedSuite: null,
                showSuiteDropdown: false,
                isLoadingSuites: false,
                // 相对于套组的基准选中集合，用于变更对比
                suiteBaseProjectIds: new Set(),
                // 更换套组确认弹窗
                showChangeConfirmModal: false,
                pendingSuiteSelection: null
			}
		},
		computed: {
			currentTableData() {
				// 为每个项目添加序号和选择状态
				return this.tableData.map((item, index) => ({
					...item,
					index: index + 1,
                    selected: this.selectedProjectIds.has(item.projectId),
                    // 仅在已选择套组时，展示与基准的差异标记
                    addedComparedToSuite: this.selectedSuite
                        ? (this.selectedProjectIds.has(item.projectId) && !this.suiteBaseProjectIds.has(item.projectId))
                        : false,
                    removedComparedToSuite: this.selectedSuite
                        ? (!this.selectedProjectIds.has(item.projectId) && this.suiteBaseProjectIds.has(item.projectId))
                        : false
				}))
			},
			selectedCount() {
				return this.currentTableData.filter(item => item.selected).length
			},
			totalCount() {
				return this.currentTableData.length
			},
			isIndeterminate() {
				const selectedCount = this.selectedCount
				return selectedCount > 0 && selectedCount < this.currentTableData.length
			}
		},
		watch: {
			selectedCount(newVal) {
				this.selectAll = newVal === this.currentTableData.length && newVal > 0
			}
		},
		async onLoad() {
			console.log('新增工单页面加载完成')
            await this.loadSuites()
			await this.loadCategories()
			await this.loadProjectList() // 默认加载全部分类的项目
		},
		methods: {
            // 加载套组列表
            async loadSuites() {
                this.isLoadingSuites = true
                try {
                    const response = await qryAssessmentSetList({})
                    console.log('获取套组列表:', response)
                    if (response && Array.isArray(response.data)) {
                        this.suiteList = response.data
                        // 默认不选择套组
                        this.selectedSuite = null
                        this.suiteBaseProjectIds = new Set()
                    } else if (response && response.data) {
                        // 兼容后端返回结构为对象但含data数组
                        const list = Array.isArray(response.data) ? response.data : []
                        this.suiteList = list
                        // 默认不选择套组
                        this.selectedSuite = null
                        this.suiteBaseProjectIds = new Set()
                    } else {
                        this.suiteList = []
                        this.selectedSuite = null
                        this.suiteBaseProjectIds = new Set()
                    }
                } catch (error) {
                    console.error('获取套组列表失败:', error)
                    uni.showToast({
                        title: '获取套组列表失败',
                        icon: 'none'
                    })
                } finally {
                    this.isLoadingSuites = false
                }
            },
            toggleSuiteDropdown() {
                this.showSuiteDropdown = !this.showSuiteDropdown
            },
            async selectSuite(suite) {
                const currentId = this.selectedSuite ? this.selectedSuite.assessmentSetId : null
                const targetId = suite ? suite.assessmentSetId : null
                if (currentId !== targetId && this.hasUnsavedChanges()) {
                    this.pendingSuiteSelection = suite
                    this.showSuiteDropdown = false
                    this.showChangeConfirmModal = true
                    return
                }
                this.selectedSuite = suite
                this.showSuiteDropdown = false
                await this.syncSelectedProjectsWithSuite()
            },
            hasUnsavedChanges() {
                // 仅在当前已选择套组时才判断
                if (!this.selectedSuite) return false
                const base = this.suiteBaseProjectIds || new Set()
                const cur = this.selectedProjectIds || new Set()
                if (base.size !== cur.size) return true
                for (const id of cur) { if (!base.has(id)) return true }
                for (const id of base) { if (!cur.has(id)) return true }
                return false
            },
            // 根据当前选择的套组，同步下方项目勾选
            async syncSelectedProjectsWithSuite() {
                try {
                    if (!this.selectedSuite) {
                        // 不选择套组：清空所有勾选
                        this.selectedProjectIds = new Set()
                        this.selectAll = false
                        this.suiteBaseProjectIds = new Set()
                        return
                    }
                    const res = await qryAssessmentSetDetail({ assessmentSetId: this.selectedSuite.assessmentSetId })
                    const detail = res && res.data ? res.data : null
                    const projectIds = detail && Array.isArray(detail.simpleProjectInfoList)
                        ? detail.simpleProjectInfoList.map(p => p.projectId)
                        : []
                    // 刷新勾选集合
                    this.selectedProjectIds = new Set(projectIds)
                    // 存储基准集合
                    this.suiteBaseProjectIds = new Set(projectIds)
                    // 更新全选状态
                    const total = this.currentTableData.length
                    const selectedCount = projectIds.length
                    this.selectAll = total > 0 && selectedCount === total
                } catch (e) {
                    console.error('获取套组详情失败:', e)
                    uni.showToast({ title: '获取套组详情失败', icon: 'none' })
                }
            },
            async confirmChangeSuite() {
                const suite = this.pendingSuiteSelection || null
                this.showChangeConfirmModal = false
                this.pendingSuiteSelection = null
                this.selectedSuite = suite
                await this.syncSelectedProjectsWithSuite()
            },
            cancelChangeSuite() {
                this.showChangeConfirmModal = false
                this.pendingSuiteSelection = null
            },
			// 加载分类数据
			async loadCategories() {
				try {
					const response = await qryAssessmentSetTypeList()
					console.log('获取分类数据:', response)
					
					if (response && response.data) {
						// 处理接口返回的分类数据，保留"全部分类"在第一位
						const categoryList = response.data.map(item => ({
							name: item.typeName || item.name || item.categoryName,
							typeId: item.typeId || item.id
						}))
						this.categories = [{ name: '全部分类', typeId: '' }, ...categoryList]
					}
				} catch (error) {
					console.error('获取分类数据失败:', error)
					uni.showToast({
						title: '获取分类数据失败',
						icon: 'none'
					})
				}
			},
			// 加载项目列表数据
			async loadProjectList(typeId = '') {
				try {
					const response = await qryAssessmentSetProjectList({ typeId })
					console.log('获取项目列表数据:', response)
					
					if (response && response.data) {
						this.tableData = response.data.map(item => ({
							projectId: item.projectId,
							projectName: item.projectName,
							typeName: item.typeName,
							categoryId: item.categoryId,
							typeId: item.typeId
						}))
						
						// 如果是加载全部分类的数据，缓存起来
						if (typeId === '') {
							this.allProjectsData = [...this.tableData]
						}
					}
				} catch (error) {
					console.error('获取项目列表失败:', error)
					uni.showToast({
						title: '获取项目列表失败',
						icon: 'none'
					})
				}
			},
			goBack() {
				console.log('返回按钮被点击')
				uni.navigateBack()
			},
			async switchCategory(categoryName) {
				this.activeCategory = categoryName
				
				// 找到对应的分类ID
				const categoryData = this.categories.find(item => item.name === categoryName)
				if (categoryData) {
					this.activeCategoryId = categoryData.typeId
					// 加载对应分类的项目列表
					await this.loadProjectList(categoryData.typeId)
				}
			},
			toggleSelectAll() {
				const shouldSelectAll = !this.selectAll || this.isIndeterminate
				this.currentTableData.forEach(item => {
					if (shouldSelectAll) {
						this.selectedProjectIds.add(item.projectId)
					} else {
						this.selectedProjectIds.delete(item.projectId)
					}
				})
				this.selectAll = shouldSelectAll
			},
			toggleItemSelect(index) {
				const item = this.currentTableData[index]
				if (this.selectedProjectIds.has(item.projectId)) {
					this.selectedProjectIds.delete(item.projectId)
				} else {
					this.selectedProjectIds.add(item.projectId)
				}
			},
			goToSuiteSettings() {
				uni.navigateTo({
					url: '/pages/assessment/suite-settings'
				})
			},
			getCategoryClass(categoryId) {
				// categoryId为0代表评估类型（蓝色），categoryId为1代表检测类型（黄色）
				return categoryId === '0' || categoryId === 0 ? 'assessment' : 'detection'
			},
            async submitOrder() {
                if (!this.selectedPatient) {
                    uni.showToast({
                        title: '请选择患者',
                        icon: 'none'
                    })
                    return
                }
                if (this.selectedProjectIds.size === 0) {
                    uni.showToast({
                        title: '请选择测评项目',
                        icon: 'none'
                    })
                    return
                }

                const payload = {
                    traineeId: this.selectedPatient.traineeId,
                    projectIds: Array.from(this.selectedProjectIds)
                }
                // 若选择了套组（无论是否修改过勾选），需传递 assessmentSetId
                if (this.selectedSuite && this.selectedSuite.assessmentSetId) {
                    payload.assessmentSetId = this.selectedSuite.assessmentSetId
                }

                try {
                    const res = await addWorkOrder(payload)
                    // 成功：code === '0000' 已在拦截器中保证，这里直接处理
                    uni.showToast({
                        title: res && res.data ? res.data : '提交成功',
                        icon: 'success'
                    })
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 1200)
                } catch (err) {
                    const msg = (err && (err.msg || err.message)) ? (err.msg || err.message) : '提交失败'
                    uni.showToast({ title: msg, icon: 'none' })
                }
            },
			// 患者搜索相关方法
			onPatientSearch() {
				// 清除之前的定时器
				if (this.searchTimer) {
					clearTimeout(this.searchTimer)
				}
				
				// 如果搜索关键词为空，隐藏下拉框
				if (!this.searchKeyword.trim()) {
					this.showPatientDropdown = false
					this.patientList = []
					return
				}
				
				// 设置防抖定时器
				this.searchTimer = setTimeout(() => {
					this.searchPatients()
				}, 300)
			},
			onSearchFocus() {
				if (this.searchKeyword.trim() && this.patientList.length > 0) {
					this.showPatientDropdown = true
				}
			},
			onSearchBlur() {
				// 延迟隐藏，以便点击下拉项目时能触发选择事件
				setTimeout(() => {
					this.showPatientDropdown = false
				}, 200)
			},
			async searchPatients() {
				if (!this.searchKeyword.trim()) {
					return
				}
				
				this.isSearching = true
				try {
					const response = await qryTraineeList({
						param: this.searchKeyword.trim()
					})
					
					if (response && response.data) {
						this.patientList = response.data
						this.showPatientDropdown = true
					} else {
						this.patientList = []
						this.showPatientDropdown = true
					}
				} catch (error) {
					console.error('搜索患者失败:', error)
					uni.showToast({
						title: '搜索患者失败',
						icon: 'none'
					})
					this.patientList = []
				} finally {
					this.isSearching = false
				}
			},
			selectPatient(patient) {
				this.selectedPatient = patient
				this.searchKeyword = patient.traineeSimDesc
				this.showPatientDropdown = false
				console.log('选中患者:', patient)
			}
		}
	}
</script>

<style lang="scss" scoped>
  /* 更换套组的自定义确认弹窗 */
  .confirm-modal-mask {
    position: fixed;
    left: 0;
    top: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.35);
    z-index: 10000;
  }
  .confirm-modal {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 420px;
    background: #FFFFFF;
    border-radius: 12px;
    box-shadow: 0 12px 32px rgba(0,0,0,0.12);
    z-index: 10001;
    overflow: hidden;
  }
  .confirm-modal-header { padding: 18px 20px 0 20px; text-align: center; }
  .confirm-title { font-family: 'Alibaba PuHuiTi'; font-size: 20px; color: #333333; }
  .confirm-modal-body { padding: 12px 20px 0 20px; text-align: center; }
  .confirm-content { font-family: 'Alibaba PuHuiTi'; font-size: 16px; color: #666666; }
  .confirm-modal-footer { display: flex; justify-content: space-between; align-items: center; padding: 20px; }
  .confirm-modal-footer .btn { min-width: 92px; height: 36px; padding: 0 16px; border-radius: 6px; display: flex; align-items: center; justify-content: center; }
  .confirm-modal-footer .btn .btn-text { font-family: 'Alibaba PuHuiTi'; font-size: 16px; }
  .confirm-modal-footer .btn.ghost { background: #FFFFFF; border: 1px solid #287FFF; }
  .confirm-modal-footer .btn.ghost .btn-text { color: #287FFF; }
  .confirm-modal-footer .btn.primary { background: #287FFF; }
  .confirm-modal-footer .btn.primary .btn-text { color: #FFFFFF; }
	.add-order-page {
		width: 100vw;
		height: 100vh;
		background: #F6F6F6;
		overflow: hidden;
		display: flex;
		flex-direction: column;
	}
	
	/* 状态栏 */
	.status-bar {
		width: 100%;
		height: 44px;
		background: #FFFFFF;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20px;
		
		.time {
			font-family: 'Alibaba PuHuiTi';
			font-size: 26px;
			color: #333333;
		}
		
		.status-icons {
			display: flex;
			align-items: center;
			gap: 50px;
		}
	}
	
	/* 顶部功能区 */
	.header {
		position: relative;
		width: 100%;
		height: 82px;
		
		.header-bg {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: #FFFFFF;
		}
		
		.back-btn {
			position: absolute;
			left: 37px;
			top: 26px;
			width: 30px;
			height: 30px;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-icon {
				width: 24px;
				height: 24px;
			}
		}
		
		.title {
			position: absolute;
			left: 50%;
			top: 26px;
			transform: translateX(-50%);
			font-family: 'Alibaba PuHuiTi';
			font-size: 30px;
			color: #333333;
			line-height: 30px;
		}
	}
	
	/* 分类选项卡容器 */
	.category-tabs-container {
		position: relative;
		width: calc(100% - 74px);
		margin: 28px 37px 0 37px;
		height: 122px;
		z-index: 1;
		
		.category-tabs-bg {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 69px;
			background: #FFFFFF;
			border-radius: 15px 15px 0 0;
		}
		
		.category-tabs {
			position: absolute;
			top: 72px;
			left: 0;
			width: 100%;
			height: 50px;
			background: #FFFFFF;
			display: flex;
			align-items: flex-end;
			padding-bottom: 8px;
			
			.category-tab {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 8px 11px;
				margin-right: 7px;
				background: #F3F8FF;
				border-radius: 6px 6px 0 0;
				
				&.active {
					background: #287FFF;
					
					.tab-text {
						color: #FFFFFF;
					}
				}
				
				.tab-text {
					font-family: 'Alibaba PuHuiTi';
					font-size: 17px;
					color: #287FFF;
					line-height: 17px;
				}
			}
		}
		
		.suite-settings {
			position: absolute;
			top: 17px;
			right: 22px;
			display: flex;
			align-items: center;
			gap: 12px;
			z-index: 1001;
			
			.suite-settings-btn {
				display: flex;
				align-items: center;
				gap: 8px;
				width: 120px;
				padding: 8px;
				background: #F3F8FF;
				border-radius: 3px;
				cursor: pointer;
				transition: background-color 0.2s;
				
				&:hover {
					background: #E6F1FF;
				}
				
				&:active {
					background: #D9ECFF;
				}
				
				.settings-icon {
					width: 22px;
					height: 22px;
				}
				
				.settings-text {
					font-family: 'Alibaba PuHuiTi';
					font-size: 17px;
					color: #287FFF;
					line-height: 17px;
				}
			}
			
			.suite-dropdown {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 189px;
				padding: 8px;
				background: #F6F6F6;
				border: 1px solid #C7C7C7;
				border-radius: 3px;
                cursor: pointer;
				
				.suite-text {
					font-family: 'Alibaba PuHuiTi';
					font-size: 17px;
					color: #333333;
					line-height: 17px;
				}
				
				.dropdown-arrow {
					width: 17px;
					height: 10px;
					background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 17 10'%3E%3Cpath d='M8.5 10 L17 0 L0 0 Z' fill='%23666666'/%3E%3C/svg%3E") no-repeat center;
					background-size: contain;
				}
			}

            .suite-options {
                position: absolute;
                top: 57px;
                right: 22px;
                width: 189px;
                max-height: 260px;
                overflow-y: auto;
                background: #FFFFFF;
                border: 1px solid #C7C7C7;
                border-radius: 6px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                z-index: 1002;
            }

            .suite-option {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 10px 12px;
                border-bottom: 1px solid #F0F0F0;
                cursor: pointer;
                transition: background-color 0.2s;
                
                &:last-child { border-bottom: none; }
                &:hover { background: #F3F8FF; }
                &.loading, &.empty { cursor: default; }
            }

            .suite-option-text {
                font-family: 'Alibaba PuHuiTi';
                font-size: 16px;
                color: #333333;
                line-height: 16px;
            }

            .suite-option-check {
                font-size: 16px;
                color: #287FFF;
                margin-left: 8px;
            }

            .suite-overlay {
                position: fixed;
                left: 0;
                top: 0;
                width: 100vw;
                height: 100vh;
                z-index: 1001;
            }
		}
		
		.patient-search {
			position: absolute;
			top: 19px;
			left: 22px;
			display: flex;
			align-items: center;
			
			.search-label {
				font-family: 'Alibaba PuHuiTi';
				font-size: 17px;
				color: #333333;
				line-height: 17px;
				width: 80px;
			}
			
			.search-input-frame {
				display: flex;
				align-items: center;
				background: #FFFFFF;
				border: 1px solid #C7C7C7;
				border-radius: 6px;
				padding-left: 11px;
				width: 514px;
				height: 33px;
				position: relative;
				
				&.search-focused {
					border-color: #287FFF;
					box-shadow: 0 0 0 2px rgba(40, 127, 255, 0.2);
				}
				
				.search-input {
					flex: 1;
					font-family: 'Alibaba PuHuiTi';
					font-size: 17px;
					color: #333333;
					border: none;
					outline: none;
					background: transparent;
					
					&::placeholder {
						color: #999999;
						font-size: 17px;
					}
				}
				
				.search-addon {
					width: 33px;
					height: 33px;
					display: flex;
					align-items: center;
					justify-content: center;
					border-left: 1px solid #EEEEEE;
					border-radius: 0 3px 3px 0;
					cursor: pointer;
					
					&:hover {
						background: #F6F6F6;
					}
					
					.search-icon {
						width: 16px;
						height: 16px;
						position: relative;
						
						&::before {
							content: '';
							position: absolute;
							top: 3px;
							left: 3px;
							width: 10px;
							height: 10px;
							border: 1px solid #999999;
							border-radius: 50%;
						}
						
						&::after {
							content: '';
							position: absolute;
							bottom: 2px;
							right: 2px;
							width: 4px;
							height: 1px;
							background: #999999;
							transform: rotate(45deg);
							border-radius: 1px;
						}
					}
				}
			}
			
			/* 患者搜索下拉框 */
			.patient-dropdown {
				position: absolute;
				top: 35px;
				left: 80px;
				width: 514px;
				max-height: 200px;
				overflow-y: auto;
				background: #FFFFFF;
				border: 1px solid #C7C7C7;
				border-top: none;
				border-radius: 0 0 6px 6px;
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
				z-index: 1000;
				
				.patient-item {
					padding: 12px 15px;
					border-bottom: 1px solid #F0F0F0;
					cursor: pointer;
					transition: background-color 0.2s;
					
					&:last-child {
						border-bottom: none;
					}
					
					&:hover {
						background: #F3F8FF;
					}
					
					.patient-info {
						font-family: 'Alibaba PuHuiTi';
						font-size: 17px;
						color: #333333;
						line-height: 17px;
					}
				}
			}
			
			/* 无结果提示 */
			.no-result-tip {
				position: absolute;
				top: 35px;
				left: 80px;
				width: 514px;
				padding: 20px 15px;
				background: #FFFFFF;
				border: 1px solid #C7C7C7;
				border-top: none;
				border-radius: 0 0 6px 6px;
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
				z-index: 1000;
				text-align: center;
				
				.no-result-text {
					font-family: 'Alibaba PuHuiTi';
					font-size: 17px;
					color: #999999;
					line-height: 17px;
				}
			}
			
			/* 搜索加载提示 */
			.search-loading {
				position: absolute;
				top: 35px;
				left: 80px;
				width: 514px;
				padding: 20px 15px;
				background: #FFFFFF;
				border: 1px solid #C7C7C7;
				border-top: none;
				border-radius: 0 0 6px 6px;
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
				z-index: 1000;
				text-align: center;
				
				.loading-text {
					font-family: 'Alibaba PuHuiTi';
					font-size: 17px;
					color: #287FFF;
					line-height: 17px;
				}
			}
		}
	}
	
	/* 表格容器 */
	.table-container {
		margin: 0 37px;
		background: #FFFFFF;
		border-radius: 0 0 15px 15px;
		margin-top: -4px;
		margin-bottom: 30px; /* 底部留白30 */
		overflow: hidden;
		border: 1px solid #EEEEEE;
		border-top: none;
		display: flex;
		flex-direction: column;
		flex: 1; /* 占满剩余空间 */
	}
	
	.table-wrapper {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
	}
	
	.table-header {
		display: flex;
		background: #F6F6F6;
		border-bottom: 1px solid #EEEEEE;
		
		.header-cell {
			font-family: 'Alibaba PuHuiTi';
			font-weight: 400;
			font-size: 22px;
			color: #333333;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 15px 11px;
			border-right: 1px solid #EEEEEE;
			
			&:last-child {
				border-right: none;
			}
		}
	}
	
.table-body {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
		.table-row {
			display: flex;
			cursor: pointer;
			transition: background-color 0.2s;
            &.changed-added { background-color: #F0FFF4; } /* 轻绿底：新增勾选 */
            &.changed-removed { background-color: #FFF5F5; } /* 轻红底：移除勾选 */
			
			&:not(:last-child) {
				border-bottom: 1px solid #EEEEEE;
			}
			
			&:hover {
				background-color: #F8F9FA;
			}
			
			&.selected {
				background-color: #F3F8FF;
			}
            /* 选中时仍保留差异高亮，放在selected规则之后以覆盖 */
            &.selected.changed-added { background-color: #E6FFED; }
            &.selected.changed-removed { background-color: #FFECEC; }
		}
	}
	
	.table-cell {
		font-family: 'Alibaba PuHuiTi';
		font-size: 22px;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 8px 11px;
		border-right: 1px solid #EEEEEE;
		min-height: 61px;
		
		&:last-child {
			border-right: none;
		}
	}
	
	/* 列宽设置 */
	.col-checkbox {
		width: 113px;
		
		.checkbox-container {
			display: flex;
			align-items: center;
			gap: 7px;
			
			.checkbox-label {
				font-family: 'Alibaba PuHuiTi';
				font-size: 22px;
				color: #333333;
				line-height: 22px;
			}
		}
		
		.checkbox {
			width: 15px;
			height: 15px;
			border: 2px solid #C7C7C7;
			border-radius: 2px;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #FFFFFF;
			
			&.checked {
				background: #287FFF;
				border-color: #287FFF;
			}
			
			&.indeterminate {
				background: #287FFF;
				border-color: #287FFF;
			}
			
			.checkbox-inner {
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.check-icon {
					width: 9px;
					height: 6px;
					position: relative;
					
					&::after {
						content: '';
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%) rotate(-45deg);
						width: 9px;
						height: 3px;
						border: 2px solid #FFFFFF;
						border-top: none;
						border-right: none;
					}
				}
				
				.minus-icon {
					width: 9px;
					height: 2px;
					background: #FFFFFF;
				}
			}
		}
	}
	
	.col-index {
		width: 146px;
	}
	
	.col-project {
		flex: 1;
		justify-content: flex-start;
		padding-left: 15px;
		
		.project-info {
			display: flex;
			align-items: center;
			gap: 15px;
			width: 100%;
			
			.project-name {
				font-family: 'Alibaba PuHuiTi';
				font-size: 22px;
				color: #333333;
				line-height: 22px;
			}
			
			.project-tag {
				padding: 7px 15px;
				border-radius: 7px;
				
				&.assessment {
					background: #EBF2FF;
					
					.tag-text {
						color: #287FFF;
					}
				}
				
				&.detection {
					background: #FFEFD2;
					
					.tag-text {
						color: #FFB130;
					}
				}
				
				.tag-text {
					font-family: 'Alibaba PuHuiTi';
					font-size: 22px;
					line-height: 22px;
				}
			}
		}
	}
	
	/* 底部操作区 */
	.footer {
		display: flex;
		align-items: center;
		justify-content: flex-end;
		gap: 8px;
		padding: 17px 28px;
		background: transparent;
		border-top: 1px solid #EEEEEE;
		flex-shrink: 0;
		
		.total-count {
			font-family: 'Alibaba PuHuiTi';
			font-size: 17px;
			color: #333333;
			line-height: 17px;
			flex: 1;
		}
		
		.footer-buttons {
			display: flex;
			gap: 8px;
			
			.btn-cancel {
				width: 100px;
				height: 40px;
				background: #FFFFFF;
				border: 1.4px solid #287FFF;
				border-radius: 6px;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.btn-text {
					font-family: 'Alibaba PuHuiTi';
					font-size: 17px;
					color: #287FFF;
					line-height: 17px;
				}
			}
			
			.btn-submit {
				width: 100px;
				height: 40px;
				background: #287FFF;
				border-radius: 6px;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.btn-text {
					font-family: 'Alibaba PuHuiTi';
					font-size: 17px;
					color: #FFFFFF;
					line-height: 17px;
				}
			}
		}
	}
</style>