<!--
 * @Description: 
 * @Author: 小雨
 * @Date: 2023-08-08 10:22:34
 * @LastEditTime: 2023-08-08 10:22:38
 * @LastEditors: 小雨
-->
<template>
	<view class="question">
		<progress :percent="100/30*state.countdown" activeColor="#10AEFF" stroke-width="20" />
		<view class="question-top center">
			<view class="question-top-title">
				{{state.questionIndex===1?'演示题':'测试题'}}
			</view>
		</view>
		<view class="question-tips">
			<view class="question-tips-audio">
				<view class="question-tips-audio-box" @click="agagin">
					<text class="question-tips-audio-box-icon iconfont">&#xe6b3;</text>语言播报
				</view>
				<view class="question-tips-audio-text">
					{{state.nameList[state.questionIndex-1]}}
				</view>
			</view>
			<view class="question-tips-left">
				<text class="question-tips-left-icon iconfont">&#xe74f;</text>计时：{{formatSeconds(state.time)}}
			</view>
			<view class="question-tips-right">
				音量调节
				<slider class="question-tips-right-slider" max="1" min="0.01" step="0.01" @change="sliderChange" value="0.5" activeColor="#41A8DE" backgroundColor="rgba(0,0,0,0.05)"
					block-color="#FFFFFF" block-size="18" />
			</view>
		</view>
		<view class="question-content">
			<view class="question-content-img">
				<view class="question-content-img-item center" @click="clicked(index)" v-for="(item,index) in 4">
					<view class="question-content-img-item-box center" :class="index===state.clicked?'question-content-img-item-box-clicked':''">
						<image class="question-content-img-item-box-value" :src="`../../static/question/test/test-${state.questionIndex}-${index+1}.png`" mode="widthFix"></image>
					</view>
					<view class="question-content-img-item-text">
						图{{index+1}}
					</view>
				</view>
				<image class="question-content-img-finger" v-if="state.questionIndex===1" :class="state.endAudio?'question-content-img-finger-ani':''" src="../../static/question/test/finger.png"
					mode="widthFix"></image>
			</view>
			<view class="question-content-btn">
				<view class="question-content-btn-previous" v-if="state.questionIndex>2" @click="pre">
					上一题
				</view>
				<!-- 	<view class="question-content-btn-next" :class="state.clicked>=0&&'question-content-btn-next-click'" @click="go">
					下一题
				</view> -->
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		onUnmounted,
		reactive,
		ref,
		watch
	} from "vue";
	import {
		onHide
	} from '@dcloudio/uni-app'
	import {
		formatSeconds
	} from "../../common/method";
	import {
		addFreeEvaInfo,
		qryFreeEvaResultPDF
	} from "../../service";
	import {
		navigateTo,
		redirectTo,
		getStorageSync,
		showLoading,
		hideLoading,
		showToast
	} from "../../common/uniTool";
	const demonstrateRef = ref(null) //演示音频
	const testRef = ref(null) //测试音频
	const testTruRef = ref(null) //测试介绍音频
	const timeRef = ref(null)
	const timeAddRef = ref(null)
	const falseAnswerRef = ref([])
	const state = reactive({
		endAudio: false,
		clicked: -1,
		percent: 100,
		time: 0,
		countdown: 30,
		clickTime: new Date(),
		resetTime: null,
		audioTime: null,
		isClick: true,
		answer: [],
		questionIndex: 1,
		trueAnswer: [1, 2, 2],
		nameList: [
			'香蕉',
			'蝴蝶',
			'香蕉'
		]
	})
	onMounted(() => {
		demonstrateRef.value = uni.createInnerAudioContext()
		testRef.value = uni.createInnerAudioContext()
		testTruRef.value = uni.createInnerAudioContext()
		demonstrateRef.value.src = `../../static/audio/demonstrate.mp3`
		testTruRef.value.onEnded(() => {
			testRef.value.src = `../../static/audio/test-${state.questionIndex}.mp3`
			state.audioTime = setTimeout(() => {
				testRef.value.play()
			}, 500)
		})
		demonstrateRef.value.onEnded(() => {
			showLoading('马上进入测试题,请做好准备！')
			setTimeout(() => {
				hideLoading()
				if (!state.isClick) {
					return
				}
				if (state.audioTime) {
					clearTimeout(state.audioTime)
					state.audioTime = null
				}
				state.answer.push(`${state.trueAnswer[state.questionIndex-1]},${0},${new Date()-state.clickTime}`)
				if (timeAddRef.value) {
					clearInterval(timeAddRef.value)
					timeAddRef.value = null
				}
				state.questionIndex++
				state.countdown = 30
				state.clickTime = new Date()
				state.clicked = -1
				state.isClick = true
				timeAdd()
				testTruRef.value.src = `../../static/audio/testTru.mp3`
				testTruRef.value.play()
			}, 500)

		})
		setTimeout(() => {
			demonstrateRef.value.play()
			setTimeout(() => {
				state.clicked = 1
				state.endAudio = true
			}, 6500)
		}, 500)
		timeRef.value = setInterval(() => {
			state.time++
		}, 1000)

		timeAdd()
	})
	onUnmounted(() => {
		console.log('销毁');
		if (demonstrateRef.value) {
			demonstrateRef.value.destroy()
			demonstrateRef.value = null
		}
		if (testRef.value) {
			testRef.value.destroy()
			testRef.value = null
		}
		if (testTruRef.value) {
			testTruRef.value.destroy()
			testTruRef.value = null
		}
		clearInterval(timeAddRef.value)
		clearInterval(timeRef.value)
		timeAddRef.value = null
		timeRef.value = null
	})
	const timeAdd = () => {
		timeAddRef.value = setInterval(() => {
			state.countdown--
		}, 1000)
	}
	const agagin = () => {
		testRef.value.stop()
		testRef.value.play()
	}
	const go = () => {
		if (!state.isClick) {
			return
		}
		if (state.audioTime) {
			clearTimeout(state.audioTime)
			state.audioTime = null
		}
		state.answer.push(`${state.trueAnswer[state.questionIndex-1]},${0},${new Date()-state.clickTime}`)
		reset()
	}

	watch(() => state.countdown, (countdown) => {
		if (countdown === 0) {
			state.answer.push(`${state.trueAnswer[state.questionIndex-1]},${0},${new Date()-state.clickTime}`)
			reset()
		}
	})

	watch(() => state.questionIndex, (questionIndex) => {
		console.log(questionIndex, '-------questionIndex');
		if (questionIndex === 4) {
			state.isClick = false
			state.questionIndex = 3
			clearInterval(timeAddRef.value)
			clearInterval(timeRef.value)
			timeAddRef.value = null
			timeRef.value = null
			testRef.value.src = '../../static/audio/test-finish.mp3'
			testRef.value.play()
			showLoading('正在进入正式测评环节！')
			testRef.value.onEnded(() => {
				hideLoading()
				redirectTo('/pages/question/index')
			})
		}
	})
	const sliderChange = (e) => {
		testRef.value.volume = e.detail.value
		console.log('value 发生变化：' + e.detail.value)
	}
	const reset = () => {
		if (timeAddRef.value) {
			clearInterval(timeAddRef.value)
			timeAddRef.value = null
		}
		state.questionIndex++
		testRef.value.src = `../../static/audio/test-${state.questionIndex}.mp3`
		state.audioTime = setTimeout(() => {
			testRef.value.play()
		}, 500)
		state.countdown = 30
		state.clickTime = new Date()
		state.clicked = -1
		state.isClick = true
		timeAdd()
	}
	const pre = () => {
		if (!state.isClick || state.questionIndex == 1) {
			return
		}
		clearInterval(timeAddRef.value)
		timeAddRef.value = null
		state.questionIndex--
		testRef.value.src = `../../static/audio/test-${state.questionIndex}.mp3`
		if (state.audioTime) {
			clearTimeout(state.audioTime)
			state.audioTime = null
		}
		state.audioTime = setTimeout(() => {
			testRef.value.play()
		}, 500)
		state.countdown = 30
		state.clickTime = new Date()
		console.log(Number(state.answer[state.answer.length - 1].split(',')[1]), '------Number(state.answer[state.answer.length - 1].split(', ')[1])');
		state.clicked = state.answer[state.answer.length - 1].split(',')[1] - 1
		state.answer.pop()
		timeAdd()
	}
	const clicked = (index) => {
		if (!state.isClick || state.questionIndex === 1) {
			return
		}
		if (state.audioTime) {
			clearTimeout(state.audioTime)
			state.audioTime = null
		}
		state.isClick = false
		clearInterval(timeAddRef.value)
		timeAddRef.value = null
		state.answer.push(`${state.trueAnswer[state.questionIndex-1]},${index+1},${new Date()-state.clickTime}`)
		state.clicked = index
		state.resetTime = setTimeout(() => {
			reset()
		}, 500)

	}
</script>

<style lang="scss">
	.question {
		width: 100vw;
		height: 100vh;
		background: linear-gradient(206deg, #D4ECF8 0%, #F2F7FC 100%);

		&-top {
			margin-top: 30rpx;

			&-title {
				font-size: 86rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #111111;
				margin-bottom: 26rpx;
			}
		}

		&-tips {
			padding: 0 102rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			&-audio {
				display: flex;
				align-items: center;

				&-box {
					border-radius: 17rpx;
					border: 4rpx solid #111111;
					font-size: 53rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
					margin-right: 39rpx;
					padding: 13rpx 26rpx;

					&-icon {
						margin-right: 17rpx;
					}
				}

				&-text {
					font-size: 53rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #111111;
				}
			}

			&-left {
				display: flex;
				align-items: center;
				font-size: 53rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #111111;

				&-icon {
					margin-right: 17rpx;
					font-size: 53rpx;
				}
			}

			&-right {
				font-size: 53rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #111111;
				display: flex;
				align-items: center;

				&-slider {
					width: 358rpx;
				}
			}
		}

		&-content {
			width: 97%;
			margin: 0 auto;
			background: #FFFFFF;
			box-shadow: 0rpx 17rpx 34rpx 0rpx rgba(212, 226, 245, 1);
			border-radius: 51rpx;
			border: 4rpx solid #FFFFFF;
			margin-top: 50rpx;

			&-img {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 68rpx 58rpx 52rpx 58rpx;

				&-finger {
					left: 50%;
					transform: translateX(-50%);
					width: 380rpx;
					top: 70%;
					position: absolute;

					&-ani {
						animation: upClick 1s ease-in-out 1;
						animation-fill-mode: forwards;
					}
				}

				&-item {
					display: flex;
					flex-direction: column;
					align-items: center;

					&-box {
						width: 500rpx;
						height: 500rpx;
						border-radius: 32rpx;
						border: 4rpx solid #999999;

						&-value {
							width: 90%;
						}

						&-clicked {
							border: 7rpx solid #41A8DE;
						}
					}

					&-text {
						font-size: 65rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #111111;
						margin-top: 30rpx;
					}
				}


			}

			&-btn {
				display: flex;
				align-items: center;
				justify-content: space-around;
				padding-bottom: 60rpx;
				width: 90%;
				margin: 0 auto;
				margin-top: 24rpx;

				&-previous {
					border-radius: 94rpx;
					border: 4rpx solid #999999;
					font-size: 56rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					padding: 40rpx 184rpx;

					&-click {
						border-color: #41A8DE;
						color: #41A8DE;
					}
				}

				&-next {
					border-radius: 94rpx;
					border: 4rpx solid #999999;
					font-size: 56rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					padding: 40rpx 184rpx;

					&-click {
						background: #41A8DE;
						color: #FFFFFF;
					}
				}
			}
		}
	}

	@keyframes upClick {
		0% {
			top: 70%;
		}

		50% {
			top: 50%;
			left: 40%;
		}

		100% {
			top: 51%;
			left: 40%;
		}
	}
</style>