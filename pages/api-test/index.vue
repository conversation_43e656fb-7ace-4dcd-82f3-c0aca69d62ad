<!--
 * @Description: API测试页面
 * @Author: 系统生成
 * @Date: 2025-08-06
 * @LastEditTime: 2025-08-06
 * @LastEditors: 系统生成
-->
<template>
	<view class="container">
		<view class="title">API测试页面</view>
		
		<view class="section">
			<view class="section-title">API日志记录功能</view>
			<view class="button-group">
				<button @click="printApiInfo" class="btn">打印所有API请求信息</button>
				<button @click="clearApiLogs" class="btn">清空API日志</button>
				<button @click="printApiStats" class="btn">打印API统计信息</button>
			</view>
		</view>
		
		<view class="section">
			<view class="section-title">API日志配置</view>
			<view class="config-info">
				<text>当前状态: </text>
				<text :style="{color: apiConfig.enabled ? '#07c160' : '#fa3534'}">
					{{ apiConfig.enabled ? '启用' : '禁用' }}
				</text>
			</view>
			<view class="config-info">
				<text>控制台打印: </text>
				<text :style="{color: apiConfig.printToConsole ? '#07c160' : '#fa3534'}">
					{{ apiConfig.printToConsole ? '启用' : '禁用' }}
				</text>
			</view>
			<view class="config-info">
				<text>本地存储: </text>
				<text :style="{color: apiConfig.saveToStorage ? '#07c160' : '#fa3534'}">
					{{ apiConfig.saveToStorage ? '启用' : '禁用' }}
				</text>
			</view>
			<view class="config-info">
				<text>最大日志数量: {{ apiConfig.maxLogs }}</text>
			</view>
			<view class="button-group">
				<button @click="getApiConfig" class="btn">获取配置</button>
				<button @click="enableApiLog" class="btn">启用日志记录</button>
				<button @click="disableApiLog" class="btn">禁用日志记录</button>
				<button @click="toggleApiLog" class="btn">切换状态</button>
			</view>
			<view class="button-group">
				<button @click="enableConsole" class="btn">启用控制台打印</button>
				<button @click="disableConsole" class="btn">禁用控制台打印</button>
			</view>
			<view class="button-group">
				<button @click="enableStorage" class="btn">启用本地存储</button>
				<button @click="disableStorage" class="btn">禁用本地存储</button>
			</view>
			<view class="button-group">
				<button @click="setMaxLogs" class="btn">设置最大日志数量</button>
			</view>
		</view>
		
		<view class="section">
			<view class="section-title">测试API请求</view>
			<view class="button-group">
				<button @click="testLoginApi" class="btn">测试登录API</button>
				<button @click="testGetUserInfoApi" class="btn">测试获取用户信息API</button>
				<button @click="testAddPPVTUserInfoApi" class="btn">测试添加PPVT用户信息API</button>
			</view>
		</view>
		
		<view class="section">
			<view class="section-title">测试结果</view>
			<view class="result-box">
				<text class="result-text">{{ testResult }}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { login, getUserInfo, addPPVTUserInfo } from '@/service/index.js';
	import {
		printAllApiInfo,
		clearAllApiLogs,
		printApiStats,
		getApiConfig,
		setApiConfig,
		enableApiLogging,
		disableApiLogging,
		toggleApiLogging,
		enableConsolePrint,
		disableConsolePrint,
		enableStorageSave,
		disableStorageSave
	} from '@/utils/printApiInfo.js';

	export default {
		data() {
			return {
				testResult: '点击按钮测试API请求',
				apiConfig: {
					enabled: true,
					printToConsole: true,
					saveToStorage: false,
					maxLogs: 100
				}
			}
		},
		onLoad() {
			// 获取当前API日志配置
			this.apiConfig = getApiConfig();
		},
		methods: {
			// 打印所有API请求信息
			printApiInfo() {
				printAllApiInfo();
				this.testResult = '已打印所有API请求信息，请查看控制台';
			},
			
			// 清空API日志
			clearApiLogs() {
				clearAllApiLogs();
				this.testResult = '已清空所有API日志';
			},
			
			// 打印API统计信息
			printApiStats() {
				printApiStats();
				this.testResult = '已打印API统计信息，请查看控制台';
			},
			
			// 获取API配置
			getApiConfig() {
				this.apiConfig = getApiConfig();
				this.testResult = '已获取API配置: ' + JSON.stringify(this.apiConfig);
			},
			
			// 启用API日志记录
			enableApiLog() {
				enableApiLogging();
				this.apiConfig = getApiConfig();
				this.testResult = 'API日志记录已启用';
			},
			
			// 禁用API日志记录
			disableApiLog() {
				disableApiLogging();
				this.apiConfig = getApiConfig();
				this.testResult = 'API日志记录已禁用';
			},
			
			// 切换API日志记录状态
			toggleApiLog() {
				toggleApiLogging();
				this.apiConfig = getApiConfig();
				this.testResult = 'API日志记录状态已切换，当前状态: ' + (this.apiConfig.enabled ? '启用' : '禁用');
			},
			
			// 启用控制台打印
			enableConsole() {
				enableConsolePrint();
				this.apiConfig = getApiConfig();
				this.testResult = '控制台打印已启用';
			},
			
			// 禁用控制台打印
			disableConsole() {
				disableConsolePrint();
				this.apiConfig = getApiConfig();
				this.testResult = '控制台打印已禁用';
			},
			
			// 启用本地存储
			enableStorage() {
				enableStorageSave();
				this.apiConfig = getApiConfig();
				this.testResult = '本地存储已启用';
			},
			
			// 禁用本地存储
			disableStorage() {
				disableStorageSave();
				this.apiConfig = getApiConfig();
				this.testResult = '本地存储已禁用';
			},
			
			// 设置最大日志数量
			setMaxLogs() {
				const maxLogs = prompt('请输入最大日志数量:', this.apiConfig.maxLogs);
				if (maxLogs && !isNaN(maxLogs)) {
					setApiConfig({ maxLogs: parseInt(maxLogs) });
					this.apiConfig = getApiConfig();
					this.testResult = '最大日志数量已设置为: ' + maxLogs;
				}
			},
			
			// 测试登录API
			async testLoginApi() {
				try {
					this.testResult = '正在测试登录API...';
					const result = await login({
						username: 'test',
						password: '123456'
					});
					this.testResult = '登录API测试完成，请查看控制台日志';
					console.log('登录API测试结果:', result);
				} catch (error) {
					this.testResult = '登录API测试失败: ' + JSON.stringify(error);
					console.error('登录API测试错误:', error);
				}
			},
			
			// 测试获取用户信息API
			async testGetUserInfoApi() {
				try {
					this.testResult = '正在测试获取用户信息API...';
					const result = await getUserInfo({
						userId: 'test'
					});
					this.testResult = '获取用户信息API测试完成，请查看控制台日志';
					console.log('获取用户信息API测试结果:', result);
				} catch (error) {
					this.testResult = '获取用户信息API测试失败: ' + JSON.stringify(error);
					console.error('获取用户信息API测试错误:', error);
				}
			},
			
			// 测试添加PPVT用户信息API
			async testAddPPVTUserInfoApi() {
				try {
					this.testResult = '正在测试添加PPVT用户信息API...';
					const result = await addPPVTUserInfo({
						phoneNumber: '13800138000',
						name: '测试用户',
						age: 25,
						gender: 1
					});
					this.testResult = '添加PPVT用户信息API测试完成，请查看控制台日志';
					console.log('添加PPVT用户信息API测试结果:', result);
				} catch (error) {
					this.testResult = '添加PPVT用户信息API测试失败: ' + JSON.stringify(error);
					console.error('添加PPVT用户信息API测试错误:', error);
				}
			}
		}
	}
</script>

<style>
.container {
	padding: 20px;
}

.title {
	font-size: 24px;
	font-weight: bold;
	text-align: center;
	margin-bottom: 30px;
}

.section {
	margin-bottom: 30px;
	padding: 15px;
	border: 1px solid #eee;
	border-radius: 8px;
}

.section-title {
	font-size: 18px;
	font-weight: bold;
	margin-bottom: 15px;
}

.button-group {
	display: flex;
	flex-wrap: wrap;
	gap: 10px;
}

.btn {
	flex: 1;
	min-width: 200px;
	padding: 10px 15px;
	background-color: #007AFF;
	color: white;
	border: none;
	border-radius: 5px;
	font-size: 14px;
}

.btn:active {
	background-color: #0056b3;
}

.result-box {
	padding: 15px;
	background-color: #f5f5f5;
	border-radius: 5px;
	min-height: 100px;
}

.result-text {
	font-size: 14px;
	line-height: 1.5;
	word-wrap: break-word;
}

.config-info {
	margin: 8px 0;
	font-size: 14px;
}

.config-info text {
	margin-right: 5px;
}
</style>