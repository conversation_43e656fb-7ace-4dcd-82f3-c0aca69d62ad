<template>
	<view class="content" v-if="state.show">
		<view class="content-back center" @click="()=>navigateBack()">
			<text class="content-back-icon  iconfont">&#xe604;</text>
		</view>
		<view class="content-top">
			<image class="content-top-img" src="/static/bg.png" mode="widthFix"></image>
			<view class="content-top-text">
				<view class="content-top-text-top">
					请填写孩子的基本信息
				</view>
			</view>
		</view>
		<view class="content-box">
			<uni-forms :modelValue="state.formData" label-position="left" label-width="200" ref="scaForm" :rules="state.rules">
				<view class="content-box-item" style="margin-bottom: 20rpx">
					<uni-forms-item label="姓名" name="userName" required class="content-box-item-one">
						<uni-easyinput type="text" v-model="state.formData.userName" placeholder="请输入真实姓名" />
					</uni-forms-item>
					<uni-forms-item label="编号" name="serNum" required class="content-box-item-one">
						<uni-easyinput type="text" v-model="state.formData.serNum" placeholder="请输入真实编号" :disabled="props.outpatientId?true:false" />
					</uni-forms-item>
				</view>
				<uni-forms-item label="性别" required name="sex" class="center" style="margin-bottom: 60rpx">
					<uni-data-checkbox v-model="state.formData.sex" :localdata="state.sex" />
				</uni-forms-item>
				<uni-forms-item label="出生年月" required name="birth" style="margin-bottom: 60rpx" class="center">
					<picker mode="date" fields="day" :value="state.formData.birth" @change="bindDateChange">
						<view v-if="state.formData.birth" class="content-box-value">{{state.formData.birth}}</view>
						<view v-else class="content-box-birth">请输入出生年月</view>
					</picker>
				</uni-forms-item>
				<uni-forms-item label="文化程度" name="stanOfCul" required class="center" style="margin-bottom: 60rpx">
					<uni-data-checkbox v-model="state.formData.stanOfCul" :localdata="state.stanOfCul" />
				</uni-forms-item>
				<uni-forms-item label="具体文化程度" name="grade" required class="center" v-if="state.formData.stanOfCul!=-1" style="margin-bottom: 60rpx">
					<uni-data-checkbox v-model="state.formData.grade" placeholder="请输入文化程度" :localdata="state.grade" />
				</uni-forms-item>
				<uni-forms-item label="临床诊断" name="remark" class="center" style="margin-bottom: 60rpx">
					<uni-easyinput type="text" v-model="state.formData.remark" placeholder="请输入临床诊断" />
				</uni-forms-item>
				<uv-button type="primary" shape="circle" text="提交" :customStyle="bottomStyle" :customTextStyle="customTextStyle" @click="formSubmit"></uv-button>
			</uni-forms>
		</view>
	</view>
</template>

<script setup>
	import {
		reactive,
		ref,
		onMounted,
		watch
	} from "vue";
	import {
		baseUrl
	} from "../../common/global";
	import {
		navigateTo,
		showToast,
		setStorage,
		navigateBack
	} from "../../common/uniTool";
	import {
		addPPVTUserInfo,
		updatePPVTUserInfo,
		qryPPVTUserInfo,
		checkPPVTUser
	} from "../../service";
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	const customTextStyle = "font-size: 40rpx;font-weight: bold;"
	const bottomStyle = 'margin-top: 10px;linear-gradient( 270deg, #1DA2FF 0%, #2073FF 100%);'
	const scaForm = ref(null)
	const props = defineProps(['outpatientId'])
	const state = reactive({
		grade: [{
			text: '幼儿园小班',
			value: '1'
		}, {
			text: '幼儿园中班',
			value: '2'
		}, {
			text: '幼儿园大班',
			value: '3'
		}],
		year: new Date().getFullYear() - 3,
		show: true,
		formData: {
			userName: '',
			birth: `${new Date().getFullYear() - 3}-01-01`,
			stanOfCul: -1,
			sex: -1,
			serNum: '',
			grade: -1,
			remark: ''
		},
		rules: {
			serNum: {
				rules: [{
					required: true,
					errorMessage: '您的编号不能为空'
				}]
			},
			stanOfCul: {
				rules: [{
					validateFunction: function (rule, value, data, callback) {
						if (value == -1) {
							callback('教育程度不能为空')
						}
						return true
					}
				}]
			},
			grade: {
				rules: [{
					validateFunction: function (rule, value, data, callback) {
						if (value == -1) {
							callback('具体文化程度不能为空')
						}
						return true
					}
				}]
			},
			userName: {
				rules: [{
					required: true,
					errorMessage: '姓名不能为空'
				}]
			},
			sex: {
				rules: [{
					validateFunction: function (rule, value, data, callback) {
						if (value == -1) {
							callback('性别不能为空')
						}
						return true
					}
				}]
			},
			birth: {
				rules: [{
					required: true,
					errorMessage: '出生日期不能为空'
				}]
			}
		},
		stanOfCul: [{
			text: '幼儿园',
			value: '1'
		}, {
			text: '小学',
			value: '2'
		}, {
			text: '初中',
			value: '3'
		}, {
			text: '高中',
			value: '4'
		}],
		sex: [{
			text: '男',
			value: 1
		}, {
			text: '女',
			value: 2
		}]
	})
	onShow(() => {
		// #ifdef APP-PLUS
		uni.showLoading({
			title: "正在进入列表..."
		})
		setTimeout(() => {
			plus.screen.lockOrientation('landscape-primary');
			uni.hideLoading();
			state.show = true
		}, 200)
		// #endif
	})
	watch(() => state.formData.stanOfCul, (stanOfCul) => {
		if (stanOfCul == 1) {
			state.grade = [{
				text: '幼儿园小班',
				value: '1'
			}, {
				text: '幼儿园中班',
				value: '2'
			}, {
				text: '幼儿园大班',
				value: '3'
			}]
			state.formData.grade = state.formData.grade || '1'
		} else if (stanOfCul == 2) {
			state.grade = [{
				text: '小学一年级',
				value: '1'
			}, {
				text: '小学二年级',
				value: '2'
			}, {
				text: '小学三年级',
				value: '3'
			}, {
				text: '小学四年级',
				value: '4'
			}, {
				text: '小学五年级',
				value: '5'
			}, {
				text: '小学六年级',
				value: '6'
			}]
			state.formData.grade = state.formData.grade || '1'
		} else if (stanOfCul == 3) {
			state.grade = [{
				text: '初一',
				value: '1'
			}, {
				text: '初二',
				value: '2'
			}, {
				text: '初三',
				value: '3'
			}]
			state.formData.grade = state.formData.grade || '1'
		} else {
			state.grade = [{
				text: '高一',
				value: '1'
			}, {
				text: '高二',
				value: '2'
			}, {
				text: '高三',
				value: '3'
			}]
			state.formData.grade = state.formData.grade || '1'
		}
	})
	onMounted(() => {
		console.log('初始化');
		if (props.outpatientId) {
			getUserInfo()
		}
	})
	const getUserInfo = () => {
		qryPPVTUserInfo({
			traineeId: props.outpatientId
		}).then(res => {
			console.log(res);
			state.formData = res.data
		})
	}
	const go = () => {
		navigateTo('/pages/report/search')
	}
	const bindDateChange = (e) => {
		state.formData.birth = e.detail.value
	}

	const formSubmit = (e) => {
		scaForm.value.validate().then(res => {
			console.log(res, '----res');
			
			// 转换数据格式以匹配新接口要求
			const requestData = {
				medicalRecordNo: res.serNum,
				traineeName: res.userName,
				sex: res.sex === 1 ? 'M' : 'F', // 1转换为M（男），2转换为F（女）
				birth: new Date(res.birth).toISOString().split('T')[0], // 格式化为YYYY-MM-DD
				stanOfCul: res.stanOfCul,
				grade: res.grade, // 添加具体文化程度字段
				desc: res.remark
			};
			
			// 如果是编辑模式，添加traineeId
			if (props.outpatientId) {
				requestData.traineeId = props.outpatientId;
			}
			
			// 为checkPPVTUser接口转换birth格式为时间戳
			res.birth = Date.parse(res.birth);
			
			checkPPVTUser(res).then(res1 => {
				console.log(res1);
				if (res1.data.isDiff) {
					uni.showModal({
						title: '当前编号的患者已存在',
						content: '是否覆盖更新已有编号的患者姓名和性别，点击确认\n如果编号信息有误，请点击取消，重新输入信息',
						success: function (res2) {
							if (res2.confirm) {
								// 如果存在outpatientId，使用更新接口，否则使用添加接口
								const apiCall = props.outpatientId ? updatePPVTUserInfo : addPPVTUserInfo;
								apiCall(requestData).then((res3) => {
									navigateBack()
								}).catch(err => {
									showToast(err.msg)
								})
							} else if (res2.cancel) {
								console.log('用户点击取消');
							}
						}
					});
				} else {
					// 如果存在outpatientId，使用更新接口，否则使用添加接口
					const apiCall = props.outpatientId ? updatePPVTUserInfo : addPPVTUserInfo;
					apiCall(requestData).then((res3) => {
						navigateBack()
					}).catch(err => {
						console.log(err);
						showToast(err.msg)
					})
				}
			}).catch(err => {
				console.log(err);
			})
		}).catch(err => {
			console.log('err', err);
		})
	}
</script>

<style lang="scss">
	.content {
		flex: 1;
		position: relative;

		&-back {
			position: absolute;
			top: 38rpx;
			left: 100rpx;
			width: 75rpx;
			height: 75rpx;
			background: #0F5CD5;
			z-index: 1;
			border-radius: 50%;
			color: #FFFFFF;
			font-size: 46rpx;
		}

		&-search {
			width: 460rpx;
			position: absolute;
			right: 70rpx;
			top: 80rpx;
		}

		&-top {
			flex: 1;

			&-img {
				width: 100%;
				position: absolute;
				transform: scaleX(-1) scaleY(-1);
				object-fit: cover;
				object-position: center;
			}

			&-text {
				position: absolute;
				bottom: 68rpx;
				left: 68rpx;
				transform: none;

				&-top {
					font-size: 86rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #FFFFFF;
				}

				&-bottom {
					font-size: 120rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #FFFFFF;

					&-text {
						font-size: 30rpx;
					}
				}
			}
		}

		&-box {
			width: 90%;
			position: absolute;
			top: 240rpx;
			left: 103rpx;
			background: #FFFFFF;
			box-shadow: 0rpx 17rpx 34rpx 0rpx rgba(0, 0, 0, 0.1);
			border-radius: 51rpx;
			padding: 106rpx 240rpx 90rpx 240rpx;

			&-birth {
				color: #999;
				font-size: 28rpx;
				border: 0.5px solid #bfbfbf;
				padding: 12rpx;
				border-radius: 4px;
				padding-left: 16rpx;
			}

			&-value {
				font-size: 28rpx;
				border: 0.5px solid #c3c3c3;
				padding: 10rpx;
				border-radius: 6px;
				color: #000;
			}

			&-item {
				display: flex;
				justify-content: space-between;

				&-one {
					width: 44%;
				}
			}
		}
	}
</style>